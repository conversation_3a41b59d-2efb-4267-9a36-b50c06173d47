<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item :label="$t('order.form.orderCode')" prop="orderCode">
        <el-input
          v-model="queryParams.orderCode"
          :placeholder="$t('order.placeholder.orderCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.articleNo')" prop="articleNo">
        <el-input
          v-model="queryParams.articleNo"
          :placeholder="$t('order.placeholder.articleNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        v-if="!isCarrierWithDepot"
        :label="$t('order.form.depot')"
        prop="depot"
      >
        <el-input
          v-model="queryParams.depot"
          :placeholder="$t('order.placeholder.depot')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('order.placeholder.compCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('order.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('order.placeholder.suppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          :placeholder="$t('order.placeholder.plantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('order.form.plantName')" prop="plantName">-->
      <!--        <el-input v-model="queryParams.plantName" :placeholder="$t('order.placeholder.plantName')" clearable size="small" @keyup.enter.native="handleQuery" />-->
      <!-- </el-form-item>-->
      <!-- <el-form-item :label="$t('order.form.unloadingNo')" prop="unloadingNo">
        <el-input
          v-model="queryParams.unloadingNo"
          :placeholder="$t('order.placeholder.unloadingNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('order.form.deliveryDate')">
        <el-date-picker
          v-model="queryParams.deliveryDate"
          size="small"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="$t('order.placeholder.deliveryDate')"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.createTime')">
        <el-date-picker
          v-model="daterangeCreateTime"
          size="small"
          value-format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          range-separator="-"
          format="yyyy-MM-dd HH:mm"
          :start-placeholder="$t('order.placeholder.startTime')"
          :end-placeholder="$t('order.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.sapUpdateTime')">
        <el-date-picker
          v-model="daterangeSapUpdateTime"
          size="small"
          value-format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          range-separator="-"
          format="yyyy-MM-dd HH:mm"
          :start-placeholder="$t('order.placeholder.startTime')"
          :end-placeholder="$t('order.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.receiveTime')">
        <el-date-picker
          v-model="daterangeReceiveTime"
          size="small"
          value-format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          range-separator="-"
          format="yyyy-MM-dd HH:mm"
          :start-placeholder="$t('order.placeholder.startTime')"
          :end-placeholder="$t('order.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item :label="$t('order.form.isRead')" prop="isRead">
        <el-select
          v-model="queryParams.isRead"
          :placeholder="$t('order.placeholder.select')"
          clearable
          size="small"
          style="width: 240px"
        >
          <el-option
            v-for="dict in yesNoDict"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('order.form.isComplete')" prop="isComplete">
        <el-select
          v-model="queryParams.isComplete"
          :placeholder="$t('order.placeholder.select')"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in yesNoDict"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >{{ $t('order.button.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t('order.button.reset')
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['datamanage:asn:add']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="exportLoading"
          @click="createASN"
        >{{ $t('order.button.createASN') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="excelDownloading"
          @click="handleOrderExcelDownload"
          >{{ $t('order.button.orderDownload') }}</el-button
        >
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printPartsInstructionNoteLoading"
          @click="printPartsInstructionNote"
          >{{ $t('order.button.printPartsInstructionNote') }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printItemTagLoading"
          @click="handlePrintItemTag"
          >{{ $t('order.button.printItemTag') }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          type="primary"
          size="mini"
          icon="el-icon-printer"
          :loading="printPickingListLoading"
          @click="handlePrintPickingList"
          >{{ $t('asn.detail.printPickingList') }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printTxtLoading"
          @click="handlePrintTxt"
          >{{ $t('order.button.printTxt') }}</el-button
        >
      </el-col>
      -->
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          :loading="confirmLoading"
          @click="orderConfirm"
        >{{ $t('order.button.orderConfirm') }}
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      />
    </el-row>

    <el-table
      v-loading="loading"
      :data="orderList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('order.table.orderCode')" align="center">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'OrderDetail',
              params: { orderId: scope.row.orderId },
              query: getQueryParams()
            }"
            class="link-type"
          >
            <span>{{ scope.row.orderCode }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('order.orderDetail.itemNo')"
        align="center"
        prop="itemNo"
      />
      <el-table-column
        :label="$t('order.orderDetail.releaseNo')"
        align="center"
        prop="deliveryScheduleNo"
      />
      <el-table-column
        :label="$t('order.orderDetail.status')"
        align="center"
        prop="status"
      />
      <el-table-column
        :label="$t('order.orderDetail.articleNo')"
        align="center"
        prop="articleNo"
      />
      <el-table-column
        :label="$t('order.orderDetail.articleName')"
        align="center"
        prop="articleName"
      />
      <el-table-column
        :label="$t('order.orderDetail.depot')"
        align="center"
        prop="depot"
      />
      <el-table-column
        :label="$t('order.orderDetail.quantity')"
        align="center"
        prop="quantity"
      />
      <el-table-column
        :label="$t('order.table.suppCode')"
        align="center"
        prop="suppCode"
      />
      <el-table-column
        :label="$t('order.table.suppName')"
        align="center"
        prop="suppName"
      />
      <el-table-column
        :label="$t('order.table.plantCode')"
        align="center"
        prop="plantCode"
      />
      <!-- <el-table-column
        :label="$t('order.table.unloadingNo')"
        align="center"
        prop="unloadingNo"
      /> -->
      <el-table-column
        :label="$t('order.orderDetail.deliveryDate')"
        align="center"
        prop="deliveryDate"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('order.table.createTime')"
        align="center"
        prop="createTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('order.table.sapUpdateTime')"
        align="center"
        prop="sapUpdateTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.sapUpdateTime, '{y}-{m}-{d} {h}:{i}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('order.table.receiveTime')"
        align="center"
        prop="receiveTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.receiveTime, '{y}-{m}-{d} {h}:{i}')
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="isRead"
        :label="$t('order.table.isRead')"
        :formatter="isReadFormat"
        align="center"
      />

      <el-table-column
        prop="isComplete"
        :label="$t('order.table.isComplete')"
        :formatter="isCompleteFormat"
        align="center"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  addOrder,
  delOrder,
  getOrder,
  listOrder,
  updateOrder,
  printItemTag,
  confirmOrder,
  printOrderTxt,
  printPartsInstructionNote,
  printPickingList,
  downloadOrderExcel
} from '@/api/datamanage/order'
import { commonDownloadFile} from '@/api/datamanage/common'
import { mapActions } from 'vuex'
import { formatNow } from '@/utils/index'

export default {
  name: 'Order',
  components: {},
  data() {
    return {
      loading: true,
      exportLoading: false,
      confirmLoading: false,
      printItemTagLoading: false,
      printTxtLoading: false,
      excelDownloading: false,
      printPartsInstructionNoteLoading: false,
      printPickingListLoading: false,
      selectedRows: [],
      checkedTblOrderItem: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      orderList: [],
      items: [],
      title: '',
      open: false,
      daterangeTimeBegin: [],
      daterangeTimeEnd: [],
      daterangeCreateTime: [],
      daterangeSapUpdateTime: [],
      daterangeReceiveTime: [],
      yesNoDict: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        articleNo: null,
        // unloadingNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        isRead: '',
        isComplete: ''
      },
      form: {},
      rules: {
        compCode: [
          {
            required: true,
            message: this.$t('order.rules.compCode'),
            trigger: 'blur'
          }
        ],
        plantCode: [
          {
            required: true,
            message: this.$t('order.rules.plantCode'),
            trigger: 'blur'
          }
        ],
        plantName: [
          {
            required: true,
            message: this.$t('order.rules.plantName'),
            trigger: 'blur'
          }
        ],
        suppCode: [
          {
            required: true,
            message: this.$t('order.rules.suppCode'),
            trigger: 'blur'
          }
        ],
        suppName: [
          {
            required: true,
            message: this.$t('order.rules.suppName'),
            trigger: 'blur'
          }
        ],
        orderCode: [
          {
            required: true,
            message: this.$t('order.rules.orderCode'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user)
      const roles = this.$store.state.user.roles
      const remark = this.$store.state.user.remark
      return roles && roles.some(role => role === 'carrier') && remark
    },
    ids() {
      return Array.from(new Set(this.selectedRows.map(row => row.orderId)))
    }
  },
  created () {
    this.getDicts('sys_yes_no').then(response => {
      this.yesNoDict = response.data
    })
    // 从路由中恢复查询条件
    this.initFromRoute()
    this.getList()
  },
  methods: {
    ...mapActions('asn', ['setAsnCreate', 'clearAsnData']),
    initFromRoute () {
      try {
        const routeQuery = this.$route?.query || {}
        // 恢复基本查询参数
        Object.keys(this.queryParams).forEach(key => {
          if (routeQuery[key] !== undefined) {
            if (key === 'pageNum' || key === 'pageSize') {
              this.queryParams[key] =
                parseInt(routeQuery[key]) || (key === 'pageNum' ? 1 : 10)
            } else if (routeQuery[key] === 'null' || routeQuery[key] === '') {
              this.queryParams[key] = null
            } else {
              this.queryParams[key] = routeQuery[key]
            }
          }
        })
        // 恢复日期范围
        if (routeQuery.daterangeCreateTime) {
          this.daterangeCreateTime = routeQuery.daterangeCreateTime.split(',')
        }
        if (routeQuery.daterangeTimeBegin) {
          this.daterangeTimeBegin = routeQuery.daterangeTimeBegin.split(',')
        }
        if (routeQuery.daterangeTimeEnd) {
          this.daterangeTimeEnd = routeQuery.daterangeTimeEnd.split(',')
        }
        if (routeQuery.daterangeSapUpdateTime) {
          this.daterangeSapUpdateTime = routeQuery.daterangeSapUpdateTime.split(',')
        }
        if (routeQuery.daterangeReceiveTime) {
          this.daterangeReceiveTime = routeQuery.daterangeReceiveTime.split(',')
        }
      } catch (e) {
        console.error('Error initializing from route:', e)
      }
    },
    getQueryParams () {
      const query = {}
      // 保存基本查询参数
      Object.keys(this.queryParams).forEach(key => {
        if (this.queryParams[key] !== null && this.queryParams[key] !== '') {
          query[key] = this.queryParams[key]
        }
      })
      // 保存日期范围
      if (this.daterangeCreateTime?.length) {
        query.daterangeCreateTime = this.daterangeCreateTime.join(',')
      }
      if (this.daterangeTimeBegin?.length) {
        query.daterangeTimeBegin = this.daterangeTimeBegin.join(',')
      }
      if (this.daterangeTimeEnd?.length) {
        query.daterangeTimeEnd = this.daterangeTimeEnd.join(',')
      }
      if (this.daterangeSapUpdateTime?.length) {
        query.daterangeSapUpdateTime = this.daterangeSapUpdateTime.join(',')
      }
      if (this.daterangeReceiveTime?.length) {
        query.daterangeReceiveTime = this.daterangeReceiveTime.join(',')
      }
      // 添加返回路径
      query.returnPath = this.$route?.fullPath || '/'
      return query
    },
    handleQuery () {
      this.queryParams.pageNum = 1
      // 更新路由查询参数
      const query = this.getQueryParams()
      this.$router.replace({ query: { ...query } }).catch(() => {})
      this.getList()
    },
    resetQuery () {
      this.daterangeTimeBegin = []
      this.daterangeTimeEnd = []
      this.daterangeCreateTime = []
      this.daterangeSapUpdateTime = []
      this.daterangeReceiveTime = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        articleNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        orderByColumn: '',
        isAsc: '',
        isRead: '',
        isComplete: ''
        // unloadingNo: null
      }
      this.resetForm('queryForm')
      // 清空路由查询参数
      this.$router.replace({ query: {} }).catch(() => {})
      this.handleQuery()
    },
    getList () {
      this.loading = true
      this.queryParams.params = {}
      if (this.daterangeTimeBegin && this.daterangeTimeBegin.length) {
        this.queryParams.params['beginTimeBegin'] = this.daterangeTimeBegin[0]
        this.queryParams.params['endTimeBegin'] = this.daterangeTimeBegin[1]
      }
      if (this.daterangeTimeEnd && this.daterangeTimeEnd.length) {
        this.queryParams.params['beginTimeEnd'] = this.daterangeTimeEnd[0]
        this.queryParams.params['endTimeEnd'] = this.daterangeTimeEnd[1]
      }
      if (this.daterangeCreateTime && this.daterangeCreateTime.length) {
        this.queryParams.params['createTimeBegin'] = this.daterangeCreateTime[0]
        this.queryParams.params['createTimeEnd'] = this.daterangeCreateTime[1]
      }
      if (this.daterangeSapUpdateTime && this.daterangeSapUpdateTime.length) {
        this.queryParams.params['sapUpdateTimeBegin'] = this.daterangeSapUpdateTime[0]
        this.queryParams.params['sapUpdateTimeEnd'] = this.daterangeSapUpdateTime[1]
      }
      if (this.daterangeReceiveTime && this.daterangeReceiveTime.length) {
        this.queryParams.params['receiveTimeBegin'] = this.daterangeReceiveTime[0]
        this.queryParams.params['receiveTimeEnd'] = this.daterangeReceiveTime[1]
      }
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    cancel () {
      this.open = false
      this.reset()
    },
    reset () {
      this.form = {
        orderId: null,
        orderCode: null,
        articleNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      }
      this.items = []
      this.resetForm('form')
    },
    handleSelectionChange (selection) {
      this.selectedRows = [...selection]
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleAdd () {
      this.reset()
      this.open = true
      this.title = this.$t('order.title.add')
    },
    handleUpdate (row) {
      this.reset()
      const orderId = row.orderId || this.ids
      getOrder(orderId).then(response => {
        this.form = response.data
        this.items = response.data.items
        this.open = true
        this.title = this.$t('order.title.update')
      })
    },
    submitForm () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.items = this.items
          if (this.form.orderId != null) {
            updateOrder(this.form).then(() => {
              this.msgSuccess(this.$t('order.success.update'))
              this.open = false
              this.getList()
            })
          } else {
            addOrder(this.form).then(() => {
              this.msgSuccess(this.$t('order.success.add'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    handleDelete (row) {
      const orderIds = row.orderId || this.ids
      this.$confirm(
        this.$t('order.confirm.delete', { orderIds }),
        this.$t('order.button.warning'),
        {
          confirmButtonText: this.$t('order.button.confirm'),
          cancelButtonText: this.$t('order.button.cancel'),
          type: 'warning'
        }
      )
        .then(() => delOrder(orderIds))
        .then(() => {
          this.getList()
          this.msgSuccess(this.$t('order.success.delete'))
        })
    },
    rowTblOrderItemIndex ({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /**
    handleAddTblOrderItem () {
      const obj = {
        itemNo: '',
        purDocType: '',
        itemType: '',
        text: '',
        delIden: '',
        shortText: '',
        oldArticleNo: '',
        articleNo: '',
        articleName: '',
        deliveryDate: '',
        quantity: '',
        unit: '',
        workbinNo: '',
        workbinName: '',
        qtyPerPack: '',
        unloadingNo: '',
        unloadingName: '',
        state: '',
        netPrice: '',
        priceUnit: '',
        orderNetWorth: '',
        currencyCode: '',
        stockLoc: '',
        locDes: '',
        locAdd: '',
        rcvName: '',
        rcvTel: '',
        inspeStrategy: '',
        zipCode: '',
        city: '',
        countryCode: '',
        addTimeZone: '',
        street2: '',
        street3: '',
        street4: ''
      }
      this.items.push(obj)
    },
    handleDeleteTblOrderItem () {
      if (this.checkedTblOrderItem.length == 0) {
        this.$alert(
          this.$t('order.alert.deleteItem'),
          this.$t('order.button.notification'),
          {
            confirmButtonText: this.$t('order.button.confirm')
          }
        )
      } else {
        this.items.splice(this.checkedTblOrderItem[0].index - 1, 1)
      }
    },
    handleTblOrderItemSelectionChange (selection) {
      if (selection.length > 1) {
        this.$refs.tblOrderItem.clearSelection()
        this.$refs.tblOrderItem.toggleRowSelection(selection.pop())
      } else {
        this.checkedTblOrderItem = selection
      }
    },
    singleExport (orderId) {
      this.exportLoading = true
      exportOrder(orderId)
        .then(response => {
          this.download(response.msg)
          this.exportLoading = false
        })
        .catch(() => {
          this.exportLoading = false
        })
    },
    */
    isReadFormat (row) {
      return this.selectDictLabel(this.yesNoDict, row.isRead)
    },
    isCompleteFormat (row) {
      return this.selectDictLabel(this.yesNoDict, row.isComplete)
    },
    createASN() {
      // 清理之前的ASN数据
      this.clearAsnData()

      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }
      let compCode = null
      let suppCode = null
      let suppName = null
      let plantCode = null
      let depot = null
      let unloadingNo = null

      // Track if all orders have the same values
      let sameComp = true
      let samePlant = true
      let sameSupp = true
      let sameDepot = true
      let sameUnloading = true

      let orderInfo = []
      for (const order of this.selectedRows) {
        if (order.isComplete === 'Y') {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.orderCompleted'),
            type: 'warning'
          })
          return
        }

        const isEmpty = value =>
          value === null || value === undefined || value === ''

        const isSameValue = (val1, val2) => {
          if (isEmpty(val1) && isEmpty(val2)) return true
          if (isEmpty(val1) || isEmpty(val2)) return false
          return val1 === val2
        }

        if (compCode === null) {
          compCode = order.compCode
          suppCode = order.suppCode
          suppName = order.suppName
          plantCode = order.plantCode
          depot = order.depot
          unloadingNo = order.unloadingNo
        } else {
          if (!isSameValue(compCode, order.compCode)) {
            sameComp = false
          }

          if (!isSameValue(suppCode, order.suppCode)) {
            sameSupp = false
          }

          if (!isSameValue(plantCode, order.plantCode)) {
            samePlant = false
          }

          if (!isSameValue(depot, order.depot)) {
            sameDepot = false
          }

          if (!isSameValue(unloadingNo, order.unloadingNo)) {
            sameUnloading = false
          }
        }

        // If any check failed, break the loop
        if (
          !sameComp ||
          !sameSupp ||
          !samePlant ||
          !sameDepot ||
          !sameUnloading
        ) {
          break
        } else {
          orderInfo.push({
            orderCode: order.orderCode,
            plantCode: order.plantCode,
            plantName: order.plantName,
            orderId: order.orderId,
            itemId: order.itemId,
            deliveryScheduleNo: order.deliveryScheduleNo
          })
        }
      }

      // Check if any validation failed and show appropriate message
      if (!sameComp) {
        this.$notify({
          title: this.$t('common.warning'),
          message: this.$t('order.alert.sameClient'),
          type: 'warning'
        })
        return
      } else if (!sameSupp) {
        this.$notify({
          title: this.$t('common.warning'),
          message: this.$t('order.alert.sameSupplier'),
          type: 'warning'
        })
        return
      } else if (!samePlant) {
        this.$notify({
          title: this.$t('common.warning'),
          message: this.$t('order.alert.samePlant'),
          type: 'warning'
        })
        return
      } else if (!sameDepot) {
        this.$notify({
          title: this.$t('common.warning'),
          message: this.$t('order.alert.sameDepot'),
          type: 'warning'
        })
        return
      } else if (!sameUnloading) {
        this.$notify({
          title: this.$t('common.warning'),
          message: this.$t('order.alert.sameUnloading'),
          type: 'warning'
        })
        return
      }

      const map = {}
      for (const order of orderInfo) {
        if (!map[order.orderId]) {
          map[order.orderId] = {
            orderId: order.orderId,
            orderCode: order.orderCode,
            plantCode: order.plantCode,
            plantName: order.plantName,
            items: [{
              itemId: order.itemId,
              deliveryScheduleNo: order.deliveryScheduleNo
            }]
          }
        } else {
          map[order.orderId].items.push({
            itemId: order.itemId,
            deliveryScheduleNo: order.deliveryScheduleNo
          })
        }
      }
      orderInfo = Object.values(map)
      this.clearAsnData()
      this.setAsnCreate({ orders: orderInfo, compCode, suppCode, suppName })
      this.$router.push({
        name: 'AsnEdit',
        query: this.getQueryParams()
      })
    },
    orderConfirm() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }
      const notNewOrderCodes = this.selectedRows
        .filter(el => el.status !== 'New')
        .map(row => row.orderCode)
      if (notNewOrderCodes.length > 0) {
        this.$alert(this.$t('order.alert.onlyNewCanConfirm', { notNewOrderCodes }))
        return
      }
      this.confirmLoading = true
      this.$confirm(
        this.$t('order.confirm.confirmAllItems'),
        this.$t('order.button.warning'),
        {
          confirmButtonText: this.$t('order.button.confirm'),
          cancelButtonText: this.$t('order.button.cancel'),
          type: 'warning'
        }
      ).then(async () => {
        await confirmOrder(this.ids)
        await this.getList()
        this.confirmLoading = false
        this.msgSuccess(this.$t('order.success.confirm'))
      }).catch((err) => {
        this.confirmLoading = false;
        this.msgError(err);
      })
    },
    handlePrintItemTag () {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }

      this.printItemTagLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时60秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 60000)

      printItemTag(this.ids)
        .then(response => {
          // 通过 window.open 打开 PDF
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          loading.close()
          this.printItemTagLoading = false
        })
        .catch(() => {
          loading.close()
          this.printItemTagLoading = false
        })
    },
    handlePrintTxt () {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }
      this.printTxtLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)

      printOrderTxt(this.ids)
        .then(response => {
          // 通过 window.open 打开 PDF
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/download?fileName=' +
              encodeURI(response.msg) +
              '&delete=true'
          )
          loading.close()
          this.printTxtLoading = false
        })
        .catch(() => {
          loading.close()
          this.printTxtLoading = false
        })
    },
    printPartsInstructionNote () {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }

      let compCode = null
      let suppCode = null
      let plantCode = null
      let depot = null
      let unloadingNo = null

      // Track if all orders have the same values
      let sameComp = true
      let samePlant = true
      let sameSupp = true
      let sameDepot = true
      let sameUnloading = true

      for (let id of this.ids) {
        for (let order of this.orderList) {
          if (order.orderId === id) {
            const isEmpty = value =>
              value === null || value === undefined || value === ''

            const isSameValue = (val1, val2) => {
              if (isEmpty(val1) && isEmpty(val2)) return true
              if (isEmpty(val1) || isEmpty(val2)) return false
              return val1 === val2
            }

            if (compCode === null) {
              compCode = order.compCode
              suppCode = order.suppCode
              plantCode = order.plantCode
              depot = order.depot
              unloadingNo = order.unloadingNo
            } else {
              if (!isSameValue(compCode, order.compCode)) {
                sameComp = false
              }

              if (!isSameValue(suppCode, order.suppCode)) {
                sameSupp = false
              }

              if (!isSameValue(plantCode, order.plantCode)) {
                samePlant = false
              }

              if (!isSameValue(depot, order.depot)) {
                sameDepot = false
              }

              if (!isSameValue(unloadingNo, order.unloadingNo)) {
                sameUnloading = false
              }
            }

            // If any check failed, break the loop
            if (
              !sameComp ||
              !sameSupp ||
              !samePlant ||
              !sameDepot ||
              !sameUnloading
            ) {
              break
            }
          }
        }

        // Check if any validation failed and show appropriate message
        if (!sameComp) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameClient'),
            type: 'warning'
          })
          return
        } else if (!sameSupp) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameSupplier'),
            type: 'warning'
          })
          return
        } else if (!samePlant) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.samePlant'),
            type: 'warning'
          })
          return
        } else if (!sameDepot) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameDepot'),
            type: 'warning'
          })
          return
        } else if (!sameUnloading) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameUnloading'),
            type: 'warning'
          })
          return
        }
      }
      this.printPartsInstructionNoteLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)

      printPartsInstructionNote(this.ids)
        .then(response => {
          // 通过 window.open 打开 PDF
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          loading.close()
          this.printPartsInstructionNoteLoading = false
        })
        .catch(() => {
          loading.close()
          this.printPartsInstructionNoteLoading = false
        })
    },
    handlePrintPickingList () {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }

      let compCode = null
      let suppCode = null
      let plantCode = null
      let depot = null
      let unloadingNo = null

      // Track if all orders have the same values
      let sameComp = true
      let samePlant = true
      let sameSupp = true
      let sameDepot = true
      let sameUnloading = true

      for (let id of this.ids) {
        for (let order of this.orderList) {
          if (order.orderId === id) {
            const isEmpty = value =>
              value === null || value === undefined || value === ''

            const isSameValue = (val1, val2) => {
              if (isEmpty(val1) && isEmpty(val2)) return true
              if (isEmpty(val1) || isEmpty(val2)) return false
              return val1 === val2
            }

            if (compCode === null) {
              compCode = order.compCode
              suppCode = order.suppCode
              plantCode = order.plantCode
              depot = order.depot
              unloadingNo = order.unloadingNo
            } else {
              if (!isSameValue(compCode, order.compCode)) {
                sameComp = false
              }

              if (!isSameValue(suppCode, order.suppCode)) {
                sameSupp = false
              }

              if (!isSameValue(plantCode, order.plantCode)) {
                samePlant = false
              }

              if (!isSameValue(depot, order.depot)) {
                sameDepot = false
              }

              if (!isSameValue(unloadingNo, order.unloadingNo)) {
                sameUnloading = false
              }
            }

            // If any check failed, break the loop
            if (
              !sameComp ||
              !sameSupp ||
              !samePlant ||
              !sameDepot ||
              !sameUnloading
            ) {
              break
            }
          }
        }

        // Check if any validation failed and show appropriate message
        if (!sameComp) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameClient'),
            type: 'warning'
          })
          return
        } else if (!sameSupp) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameSupplier'),
            type: 'warning'
          })
          return
        } else if (!samePlant) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.samePlant'),
            type: 'warning'
          })
          return
        } else if (!sameDepot) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameDepot'),
            type: 'warning'
          })
          return
        } else if (!sameUnloading) {
          this.$notify({
            title: this.$t('common.warning'),
            message: this.$t('order.alert.sameUnloading'),
            type: 'warning'
          })
          return
        }
      }
      this.printPickingListLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)

      printPickingList(this.ids)
        .then(response => {
          // 通过 window.open 打开 PDF
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          loading.close()
          this.printPickingListLoading = false
        })
        .catch(() => {
          loading.close()
        this.printPickingListLoading = false
        })
    },
    handleOrderExcelDownload() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('order.alert.selectOrder'))
        return
      }
      this.excelDownloading = true;
      this.$confirm(
        this.$t('order.confirm.downloadAllItems'),
        this.$t('order.button.warning'),
        {
          confirmButtonText: this.$t('order.button.confirm'),
          cancelButtonText: this.$t('order.button.cancel'),
          type: 'warning'
        }
      ).then(() => {
        downloadOrderExcel(this.ids)
        .then((res) => {
          commonDownloadFile(res.msg, `${this.$t('route.order')}${formatNow()}.xlsx`)
          .then(() => {
            this.excelDownloading = false;
          })
        })
        .catch((err) => {
          this.excelDownloading = false;
          this.msgError(err);
        })
      }).catch(() => {
        this.excelDownloading = false;
      })
    }
  }
}
</script>
