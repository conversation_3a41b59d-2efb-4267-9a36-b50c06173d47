import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/datamanage/order/list',
    method: 'get',
    params: query
  })
}

// 查询行项目
export function listOrderItems (query) {
  return request({
    url: '/datamanage/order/listItems',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder (orderId) {
  return request({
    url: '/datamanage/order/' + orderId,
    method: 'get'
  })
}

// 新增订单
export function addOrder (data) {
  return request({
    url: '/datamanage/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder (data) {
  return request({
    url: '/datamanage/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder (orderId) {
  return request({
    url: '/datamanage/order/' + orderId,
    method: 'delete'
  })
}

// 导出订单
export function exportOrder (orderId) {
  return request({
    url: '/datamanage/order/export/' + orderId,
    method: 'get'
  })
}

// 查询订单详细(不包含行项目)
export function getOrderOnly (orderId) {
  return request({
    url: '/datamanage/order/head/' + orderId,
    method: 'get'
  })
}

// 导出ASN模板
export function exportAsnTemp (orderId) {
  return request({
    url: '/datamanage/order/exportAsnTemp/' + orderId,
    method: 'get'
  })
}

export function printItemTag(ids) {
  return request({
    url: '/datamanage/order/printOrderPdf',
    method: 'post',
    data: ids
  })
}
export function printPartsInstructionNote(ids) {
  return request({
    url: '/datamanage/order/printInstructPdf',
    method: 'post',
    data: ids
  })
}
export function printPickingList(ids) {
  return request({
    url: '/datamanage/order/printPickingList',
    method: 'post',
    data: ids
  })
}

export function printOrderTxt(ids) {
  return request({
    url: '/datamanage/order/printOrderTxt',
    method: 'post',
    data: ids
  })
}

// 确认订单
export function confirmOrder(ids) {
  return request({
    url: '/datamanage/order/confirm',
    method: 'post',
    data: ids
  })
}

// 下载订单Excel
export function downloadOrderExcel(ids) {
  return request({
    url: '/datamanage/order/downloadExcel',
    method: 'post',
    data: ids
  })
}
