import axios from 'axios'
import { downloadFile } from '@/utils/fileDownload.js'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API
})

export function commonDownloadFile(filePath, fileName) {
  const url = `/common/download?fileName=${filePath}&delete=true`
  return service
    .get(url, {
      responseType: 'blob'
    })
    .then((res) => {
      const blob = new Blob([res.data])
      return downloadFile(blob, fileName)
    })
}
