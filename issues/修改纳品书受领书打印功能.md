# 修改纳品书/受领书打印功能

## 任务背景
根据提供的Vue模板样式，修改TblAsnServiceImpl中的printNpsSls方法和npsjsls.jrxml模板，实现纳品书/受领书的打印功能。

## Vue模板特点
- 左右分栏布局（纳品书/受领书）
- 包含公司信息、供应商信息、交货日期、SAP交货单号
- 条码和二维码显示
- 表格显示物料明细（每页5行）
- 页脚包含工厂信息和受领印

## 执行计划
1. 修改printNpsSls方法使用selectTblAsnForPrint
2. 创建genPrintDataForNpsSls方法
3. 重新设计npsjsls.jrxml模板
4. 添加必要的参数定义
5. 测试和调试

## 进度记录
- [x] 创建任务记录文件
- [x] 修改printNpsSls方法
- [x] 创建genPrintDataForNpsSls方法
- [x] 重新设计npsjsls.jrxml模板
- [ ] 测试功能

## 已完成的修改

### 1. TblAsnServiceImpl.printNpsSls方法
- 修改为使用selectTblAsnForPrint获取完整数据
- 调用新的genPrintDataForNpsSls方法

### 2. genPrintDataForNpsSls方法
- 生成符合Vue模板需求的数据结构
- 包含asnno、sapasnNo、supplierName、deliveryDate等字段
- 处理物料明细：partNo、partDescription、orderNo等
- 实现每页5行的分页逻辑（预留给前端处理）

### 3. npsjsls.jrxml模板
- 重新设计为A4横向左右分栏布局
- 左侧：纳品书，右侧：受领书
- 包含公司信息、供应商信息、交货日期等
- 表格显示物料明细信息
- 添加条码和二维码
- 页脚包含工厂信息和受领印

## 注意事项
- 公司信息字段暂时留空
- 模板使用JasperReports的标准数据源，每行显示一条物料记录
- 条码和二维码使用ASN号码生成
