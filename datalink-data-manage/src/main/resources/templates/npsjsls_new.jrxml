<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="纳品书受领书" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="98c10f30-ede5-4f7e-8379-ae5c93c9fb46">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	
	<!-- 参数定义 -->
	<parameter name="asnno" class="java.lang.String"/>
	<parameter name="sapasnNo" class="java.lang.String"/>
	<parameter name="supplierName" class="java.lang.String"/>
	<parameter name="deliveryDate" class="java.lang.String"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="companyAddress" class="java.lang.String"/>
	<parameter name="c_Phone" class="java.lang.String"/>
	<parameter name="c_FAX" class="java.lang.String"/>
	<parameter name="phone" class="java.lang.String"/>
	<parameter name="fax" class="java.lang.String"/>
	<parameter name="factoryCode" class="java.lang.String"/>
	<parameter name="deliveryAddress" class="java.lang.String"/>
	<parameter name="userLoginName" class="java.lang.String"/>
	<parameter name="asnList" class="java.util.List"/>
	
	<queryString>
		<![CDATA[]]>
	</queryString>
	
	<!-- 字段定义 -->
	<field name="lineNo" class="java.lang.String"/>
	<field name="partNo" class="java.lang.String"/>
	<field name="partDescription" class="java.lang.String"/>
	<field name="orderNo" class="java.lang.String"/>
	<field name="orderLineNo" class="java.lang.String"/>
	<field name="releaseNo" class="java.lang.String"/>
	<field name="shipQty" class="java.math.BigDecimal"/>
	<field name="remark" class="java.lang.String"/>
	
	<background>
		<band splitType="Stretch"/>
	</background>
	
	<pageHeader>
		<band height="150" splitType="Stretch">
			<!-- 左侧纳品书标题 -->
			<staticText>
				<reportElement x="0" y="0" width="400" height="25" uuid="title-left"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[纳品书]]></text>
			</staticText>
			
			<!-- 右侧受领书标题 -->
			<staticText>
				<reportElement x="402" y="0" width="400" height="25" uuid="title-right"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[受领书]]></text>
			</staticText>
			
			<!-- 分隔线 -->
			<line>
				<reportElement x="401" y="0" width="1" height="150" uuid="separator"/>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			
			<!-- 左侧信息区域 -->
			<staticText>
				<reportElement x="10" y="30" width="60" height="15" uuid="label-company-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[公司名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="70" y="30" width="150" height="15" uuid="field-company-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="10" y="50" width="60" height="15" uuid="label-supplier-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[供应商:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="70" y="50" width="150" height="15" uuid="field-supplier-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{supplierName}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="10" y="70" width="80" height="15" uuid="label-delivery-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[交货日期:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="70" width="100" height="15" uuid="field-delivery-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryDate}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="10" y="90" width="100" height="15" uuid="label-sap-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[SAP交货单号:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="110" y="90" width="120" height="15" uuid="field-sap-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sapasnNo}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="10" y="110" width="60" height="15" uuid="label-asn-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[ASN号:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="70" y="110" width="120" height="15" uuid="field-asn-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{asnno}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="10" y="130" width="80" height="15" uuid="label-printed-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[Printed By:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="90" y="130" width="100" height="15" uuid="field-printed-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userLoginName}]]></textFieldExpression>
			</textField>
			
			<!-- 右侧信息区域 -->
			<staticText>
				<reportElement x="412" y="30" width="60" height="15" uuid="label-supplier-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[供应商:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="472" y="30" width="150" height="15" uuid="field-supplier-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{supplierName}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="412" y="50" width="60" height="15" uuid="label-company-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[公司名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="472" y="50" width="150" height="15" uuid="field-company-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="412" y="70" width="80" height="15" uuid="label-delivery-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[交货日期:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="492" y="70" width="100" height="15" uuid="field-delivery-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryDate}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="412" y="90" width="100" height="15" uuid="label-sap-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[SAP交货单号:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="512" y="90" width="120" height="15" uuid="field-sap-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sapasnNo}]]></textFieldExpression>
			</textField>
			
			<staticText>
				<reportElement x="412" y="110" width="80" height="15" uuid="label-printed-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[Printed By:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="492" y="110" width="100" height="15" uuid="field-printed-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userLoginName}]]></textFieldExpression>
			</textField>
			
			<!-- 条码区域 -->
			<componentElement>
				<reportElement x="250" y="30" width="140" height="20" uuid="barcode-left"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			
			<componentElement>
				<reportElement x="652" y="30" width="140" height="20" uuid="barcode-right"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
		</band>
	</pageHeader>
	
	<columnHeader>
		<band height="25" splitType="Stretch">
			<!-- 左侧表头 -->
			<staticText>
				<reportElement x="0" y="0" width="30" height="25" uuid="header-no-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="80" height="25" uuid="header-part-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[客户部品番号
（零件号）
PART NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="0" width="150" height="25" uuid="header-desc-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[订单号-明细-连番
部件名称
PART NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="50" height="25" uuid="header-qty-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[发注
数量
PQ QTY]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="0" width="40" height="25" uuid="header-end-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[完纳
ENEND]]></text>
			</staticText>
			<staticText>
				<reportElement x="350" y="0" width="50" height="25" uuid="header-note-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[备注
NOTE]]></text>
			</staticText>
			
			<!-- 右侧表头 -->
			<staticText>
				<reportElement x="402" y="0" width="30" height="25" uuid="header-no-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="432" y="0" width="80" height="25" uuid="header-part-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[客户部品番号
（零件号）
PART NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="512" y="0" width="150" height="25" uuid="header-desc-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[订单号-明细-连番
部件名称
PART NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="662" y="0" width="50" height="25" uuid="header-qty-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[发注
数量
PQ QTY]]></text>
			</staticText>
			<staticText>
				<reportElement x="712" y="0" width="40" height="25" uuid="header-end-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[完纳
ENEND]]></text>
			</staticText>
			<staticText>
				<reportElement x="752" y="0" width="50" height="25" uuid="header-note-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[备注
NOTE]]></text>
			</staticText>
		</band>
	</columnHeader>

	<detail>
		<band height="20" splitType="Stretch">
			<!-- 左侧数据行 -->
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="30" height="20" uuid="data-no-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="30" y="0" width="80" height="20" uuid="data-part-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="110" y="0" width="150" height="20" uuid="data-desc-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo} + "-" + ($F{orderLineNo} != null ? $F{orderLineNo} : "") + "-" + ($F{releaseNo} != null ? $F{releaseNo} : "") + "\n" + ($F{partDescription} != null ? $F{partDescription} : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="260" y="0" width="50" height="20" uuid="data-qty-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="310" y="0" width="40" height="20" uuid="data-end-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="350" y="0" width="50" height="20" uuid="data-note-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>

			<!-- 右侧数据行 -->
			<textField isBlankWhenNull="true">
				<reportElement x="402" y="0" width="30" height="20" uuid="data-no-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="432" y="0" width="80" height="20" uuid="data-part-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="512" y="0" width="150" height="20" uuid="data-desc-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo} + "-" + ($F{orderLineNo} != null ? $F{orderLineNo} : "") + "-" + ($F{releaseNo} != null ? $F{releaseNo} : "") + "\n" + ($F{partDescription} != null ? $F{partDescription} : "")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="662" y="0" width="50" height="20" uuid="data-qty-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="712" y="0" width="40" height="20" uuid="data-end-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="752" y="0" width="50" height="20" uuid="data-note-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
		</band>
	</detail>

	<pageFooter>
		<band height="80" splitType="Stretch">
			<!-- 左侧页脚 -->
			<staticText>
				<reportElement x="10" y="10" width="100" height="15" uuid="footer-factory-label-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[工厂(库存地点):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="110" y="10" width="200" height="15" uuid="footer-factory-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{factoryCode} + "(" + $P{deliveryAddress} + ")"]]></textFieldExpression>
			</textField>

			<staticText>
				<reportElement x="10" y="30" width="60" height="15" uuid="footer-note-label-left"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[备注:]]></text>
			</staticText>

			<staticText>
				<reportElement x="10" y="50" width="100" height="25" uuid="footer-stamp-left"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[受领印(完结时)]]></text>
			</staticText>

			<!-- 右侧页脚 -->
			<staticText>
				<reportElement x="412" y="10" width="100" height="15" uuid="footer-factory-label-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[工厂(库存地点):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="512" y="10" width="200" height="15" uuid="footer-factory-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{factoryCode} + "(" + $P{deliveryAddress} + ")"]]></textFieldExpression>
			</textField>

			<staticText>
				<reportElement x="412" y="30" width="60" height="15" uuid="footer-note-label-right"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[备注:]]></text>
			</staticText>

			<staticText>
				<reportElement x="412" y="50" width="100" height="25" uuid="footer-stamp-right"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[受领印(完结时)]]></text>
			</staticText>

			<!-- 二维码区域 -->
			<componentElement>
				<reportElement x="320" y="30" width="40" height="40" uuid="qr-left"/>
				<jr:QRCode xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:QRCode>
			</componentElement>

			<componentElement>
				<reportElement x="722" y="30" width="40" height="40" uuid="qr-right"/>
				<jr:QRCode xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:QRCode>
			</componentElement>
		</band>
	</pageFooter>
</jasperReport>
