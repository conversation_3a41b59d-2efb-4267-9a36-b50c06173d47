<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblAsnMapper">

    <resultMap type="TblAsn" id="TblAsnResult">
        <result property="asnId"    column="Asn_ID"    />
        <result property="asnCode"    column="Asn_Code"    />
        <result property="docNo"    column="Doc_No"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="docDate"    column="Doc_Date"    />
        <result property="planDeliveryDate"    column="Plan_Delivery_Date"    />
        <result property="deliveryDate"    column="Delivery_Date"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
    </resultMap>

    <resultMap id="TblAsnTblAsnItemResult" type="TblAsn" extends="TblAsnResult">
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblAsnItemArticleResult" />
    </resultMap>

    <resultMap id="TblAsnTblAsnItemApiResult" type="TblApiAsn" extends="TblAsnResult">
        <collection property="detail" ofType="com.datalink.api.domain.TblApiAsnItem" select="selectTblApiAsnItemByAsnId" column="Asn_ID"/>
    </resultMap>

    <resultMap id="TblAsnForPrintResult" type="TblAsn" extends="TblAsnResult">
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblAsnItemForPrintResult" />
    </resultMap>

    <resultMap id="TblAsnItemForPrintResult" type="TblAsnItem" extends="TblAsnItemResult">
        <collection property="articles" notNullColumn="sub_sub_Article_ID" javaType="java.util.List" resultMap="TblAsnArticleResult" />
    </resultMap>

    <select id="selectTblApiAsnItemByAsnId" parameterType="Long" resultMap="TblApiAsnItemResult">
        <include refid="selectTblAsnItemVo"/>
        where Asn_ID = #{asnId}
    </select>

    <select id="selectTblAsnItemByAsnId" parameterType="Long" resultMap="TblAsnItemResult">
        <include refid="selectTblAsnItemVo"/>
        where Asn_ID = #{asnId}
    </select>

    <select id="selectTblAsnItemArticleByAsnId" parameterType="Long" resultMap="TblAsnItemArticleResult">
        <include refid="selectTblAsnItemVo"/>
        where Asn_ID = #{asnId}
    </select>

    <select id="selectTblAsnItemByItemId" parameterType="Long" resultMap="TblAsnItemResult">
        <include refid="selectTblAsnItemVo"/>
        where Item_ID = #{itemId}
    </select>

    <resultMap type="TblAsnItem" id="TblAsnItemResult">
        <result property="itemId"    column="sub_Item_ID"    />
        <result property="docNo"    column="sub_Doc_No"    />
        <result property="dnNo"    column="sub_Dn_No"    />
        <result property="orderCode"    column="sub_Order_Code"    />
        <result property="plantCode"    column="sub_Plant_Code"    />
        <result property="plantName"    column="sub_Plant_Name"    />
        <result property="unloadingNo"    column="sub_Unloading_No"    />
        <result property="unloadingName"    column="sub_Unloading_Name"    />
        <result property="sendLocNo"    column="sub_Send_Loc_No"    />
        <result property="sendLocName"    column="sub_Send_Loc_Name"    />
        <result property="rcvLocNo"    column="sub_Rcv_Loc_No"    />
        <result property="rcvLocName"    column="sub_Rcv_Loc_Name"    />
        <result property="asnId"    column="sub_Asn_ID"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
    </resultMap>

    <resultMap type="TblAsnArticle" id="TblAsnArticleResult">
        <result property="articleId"    column="sub_sub_Article_ID"    />
        <result property="docNo"    column="sub_sub_Doc_No"    />
<!--        <result property="dnNo"    column="sub_sub_Dn_No"    />-->
        <result property="articleNo"    column="sub_sub_Article_No"    />
        <result property="articleName"    column="sub_sub_Article_Name"    />
        <result property="quantity"    column="sub_sub_Quantity"    />
        <result property="unit"    column="sub_sub_Unit"    />
        <result property="batchNo"    column="sub_sub_Batch_No"    />
        <result property="orderLineNo"    column="sub_sub_Order_Line_No"    />
        <result property="qtyPerPack"    column="sub_sub_Qty_Per_Pack"    />
        <result property="packQty"    column="sub_sub_Pack_Qty"    />
        <result property="nonStd"    column="sub_sub_Non_Std"    />
        <result property="startWith"    column="sub_sub_Start_With"    />
        <result property="endWith"    column="sub_sub_End_With"    />
        <result property="itemId"    column="sub_sub_Item_ID"    />
        <result property="deliveryScheduleNo"    column="sub_sub_Delivery_Schedule_No"    />
        <result property="createTime"    column="sub_sub_Create_Time"    />
        <result property="createBy"    column="sub_sub_Create_By"    />
        <result property="updateTime"    column="sub_sub_Update_Time"    />
        <result property="updateBy"    column="sub_sub_Update_By"    />
    </resultMap>

    <resultMap id="TblApiAsnArticleResult" type="TblApiAsnArticle" extends="TblAsnArticleResult">
    </resultMap>

    <resultMap id="TblApiAsnItemResult" type="TblApiAsnItem" extends="TblAsnItemResult">
        <collection property="articles" ofType="com.datalink.api.domain.TblApiAsnArticle" select="selectTblApiAsnArticleByItemId" column="sub_Item_ID"/>
    </resultMap>

    <resultMap id="TblAsnItemArticleResult" type="TblAsnItem" extends="TblAsnItemResult">
        <collection property="articles" ofType="com.datalink.datamanage.domain.TblAsnArticle" select="selectTblAsnArticleByItemId" column="sub_Item_ID"/>
    </resultMap>



    <select id="selectTblApiAsnArticleByItemId" parameterType="Long" resultMap="TblApiAsnArticleResult">
        <include refid="selectTblAsnArticleVo"/>
        where Item_ID = #{itemId}
    </select>

    <select id="selectTblAsnArticleByItemId" parameterType="Long" resultMap="TblAsnArticleResult">
        <include refid="selectTblAsnArticleVo"/>
        where Item_ID = #{itemId}
    </select>



    <sql id="selectTblAsnVo">
        select Asn_ID, Asn_Code, Doc_No, Comp_Code, Supp_Code, Supp_Name, Doc_Date, Plan_Delivery_Date, Delivery_Date, Create_Time, Create_By, Update_Time, Update_By, Direction, Kafka_Status from tbl_asn a
    </sql>

    <sql id="selectTblAsnItemVo">
        select Item_ID as sub_Item_ID, Doc_No as sub_Doc_No, Dn_No as sub_Dn_No, Order_Code as sub_Order_Code, Plant_Code as sub_Plant_Code, Plant_Name as sub_Plant_Name, Unloading_No as sub_Unloading_No, Unloading_Name as sub_Unloading_Name, Send_Loc_No as sub_Send_Loc_No, Send_Loc_Name as sub_Send_Loc_Name, Rcv_Loc_No as sub_Rcv_Loc_No, Rcv_Loc_Name as sub_Rcv_Loc_Name, Asn_ID as sub_Asn_ID, Create_Time as sub_Create_Time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By from tbl_asn_item
    </sql>

    <sql id="selectTblAsnArticleVo">
        select Article_ID as sub_sub_Article_ID, Doc_No as sub_sub_Doc_No, Dn_No as sub_sub_Dn_No, Article_No as sub_sub_Article_No, Article_Name as sub_sub_Article_Name, Quantity as sub_sub_Quantity, Unit as sub_sub_Unit, Batch_No as sub_sub_Batch_No, Order_Line_No as sub_sub_Order_Line_No, Qty_Per_Pack as sub_sub_Qty_Per_Pack, Pack_Qty as sub_sub_Pack_Qty, Non_Std as sub_sub_Non_Std, Start_With as sub_sub_Start_With, End_With as sub_sub_End_With, Item_ID as sub_sub_Item_ID, Delivery_Schedule_No as sub_sub_Delivery_Schedule_No, Create_Time as sub_sub_Create_Time, Create_By as sub_sub_Create_By, Update_Time as sub_sub_Update_Time, Update_By as sub_sub_Update_By from tbl_asn_article
    </sql>

    <select id="selectTblAsnList" parameterType="TblAsn" resultMap="TblAsnResult">
                select distinct a.*
                from (
                    <include refid="selectTblAsnVo"/>
                ) a
                <if test="is223X">
                    inner join tbl_asn_item i on a.Asn_ID = i.Asn_ID and i.Plant_Code = '223X'
                </if>
                <where>
                    <if test="tblAsn.asnCode != null  and tblAsn.asnCode != ''"> and a.Asn_Code like concat('%', #{tblAsn.asnCode}, '%')</if>
                    <if test="tblAsn.docNo != null  and tblAsn.docNo != ''"> and a.Doc_No like concat('%', #{tblAsn.docNo}, '%')</if>
                    <if test="tblAsn.compCode != null  and tblAsn.compCode != ''"> and a.Comp_Code like concat('%', #{tblAsn.compCode}, '%')</if>
                    <if test="tblAsn.suppCode != null  and tblAsn.suppCode != ''"> and a.Supp_Code like concat('%', #{tblAsn.suppCode}, '%')</if>
                    <if test="tblAsn.suppName != null  and tblAsn.suppName != ''"> and a.Supp_Name like concat('%', #{tblAsn.suppName}, '%')</if>
                    <if test="tblAsn.params.beginDocDate != null and tblAsn.params.beginDocDate != '' and tblAsn.params.endDocDate != null and tblAsn.params.endDocDate != ''"> and a.Doc_Date between #{tblAsn.params.beginDocDate} and #{tblAsn.params.endDocDate}</if>
                    <if test="tblAsn.params.beginPlanDeliveryDate != null and tblAsn.params.beginPlanDeliveryDate != '' and tblAsn.params.endPlanDeliveryDate != null and tblAsn.params.endPlanDeliveryDate != ''"> and a.Plan_Delivery_Date between #{tblAsn.params.beginPlanDeliveryDate} and #{tblAsn.params.endPlanDeliveryDate}</if>
                    <if test="tblAsn.params.beginDeliveryDate != null and tblAsn.params.beginDeliveryDate != '' and tblAsn.params.endDeliveryDate != null and tblAsn.params.endDeliveryDate != ''"> and a.Delivery_Date between #{tblAsn.params.beginDeliveryDate} and #{tblAsn.params.endDeliveryDate}</if>
                    <if test="tblAsn.createTime != null "> and a.Create_Time = #{tblAsn.createTime}</if>
                    <if test="tblAsn.createBy != null  and tblAsn.createBy != ''"> and a.Create_By = #{tblAsn.createBy}</if>
                    <if test="tblAsn.updateTime != null "> and a.Update_Time = #{tblAsn.updateTime}</if>
                    <if test="tblAsn.updateBy != null  and tblAsn.updateBy != ''"> and a.Update_By = #{tblAsn.updateBy}</if>
                    <if test="tblAsn.direction != null  and tblAsn.direction != ''"> and a.Direction = #{tblAsn.direction}</if>
                    <if test="tblAsn.kafkaStatus != null  and tblAsn.kafkaStatus != ''"> and a.Kafka_Status = #{tblAsn.kafkaStatus}</if>
                    <!-- 数据范围过滤 -->
                    ${tblAsn.params.dataScope}
                </where>
            </select>

    <select id="selectTblAsnById" parameterType="TblAsn" resultMap="TblAsnTblAsnItemResult">
        select a.Asn_ID, a.Asn_Code, a.Doc_No, a.Comp_Code, a.Supp_Code, a.Supp_Name, a.Doc_Date, a.Plan_Delivery_Date, a.Delivery_Date, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status,
            b.Item_ID as sub_Item_ID, b.Doc_No as sub_Doc_No, b.Dn_No as sub_Dn_No, b.Order_Code as sub_Order_Code, b.Plant_Code as sub_Plant_Code, b.Plant_Name as sub_Plant_Name, b.Unloading_No as sub_Unloading_No, b.Unloading_Name as sub_Unloading_Name, b.Send_Loc_No as sub_Send_Loc_No, b.Send_Loc_Name as sub_Send_Loc_Name, b.Rcv_Loc_No as sub_Rcv_Loc_No, b.Rcv_Loc_Name as sub_Rcv_Loc_Name, b.Asn_ID as sub_Asn_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_asn a
        left join tbl_asn_item b on b.Asn_ID = a.Asn_ID
        where a.Asn_ID = #{asnId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectTblAsnByAsnCode" parameterType="TblAsn" resultMap="TblAsnTblAsnItemResult">
        select a.Asn_ID, a.Asn_Code, a.Doc_No, a.Comp_Code, a.Supp_Code, a.Supp_Name, a.Doc_Date, a.Plan_Delivery_Date, a.Delivery_Date, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status,
            b.Item_ID as sub_Item_ID, b.Doc_No as sub_Doc_No, b.Dn_No as sub_Dn_No, b.Order_Code as sub_Order_Code, b.Plant_Code as sub_Plant_Code, b.Plant_Name as sub_Plant_Name, b.Unloading_No as sub_Unloading_No, b.Unloading_Name as sub_Unloading_Name, b.Send_Loc_No as sub_Send_Loc_No, b.Send_Loc_Name as sub_Send_Loc_Name, b.Rcv_Loc_No as sub_Rcv_Loc_No, b.Rcv_Loc_Name as sub_Rcv_Loc_Name, b.Asn_ID as sub_Asn_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_asn a
        left join tbl_asn_item b on b.Asn_ID = a.Asn_ID
        where a.Asn_Code = #{asnCode}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectTblAsnWithItemList" parameterType="Long" resultMap="TblAsnTblAsnItemResult">
        select a.Asn_ID, a.Asn_Code, a.Doc_No, a.Comp_Code, a.Supp_Code, a.Supp_Name, a.Doc_Date, a.Plan_Delivery_Date, a.Delivery_Date, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status,
               b.Item_ID as sub_Item_ID, b.Doc_No as sub_Doc_No, b.Dn_No as sub_Dn_No, b.Order_Code as sub_Order_Code, b.Plant_Code as sub_Plant_Code, b.Plant_Name as sub_Plant_Name, b.Unloading_No as sub_Unloading_No, b.Unloading_Name as sub_Unloading_Name, b.Send_Loc_No as sub_Send_Loc_No, b.Send_Loc_Name as sub_Send_Loc_Name, b.Rcv_Loc_No as sub_Rcv_Loc_No, b.Rcv_Loc_Name as sub_Rcv_Loc_Name, b.Asn_ID as sub_Asn_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_asn a
                 left join tbl_asn_item b on b.Asn_ID = a.Asn_ID
        <where>
            <if test="asnCode != null  and asnCode != ''"> and a.Asn_Code like concat('%', #{asnCode}, '%')</if>
            <if test="docNo != null  and docNo != ''"> and a.Doc_No like concat('%', #{docNo}, '%')</if>
            <if test="compCode != null  and compCode != ''"> and a.Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and a.Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and a.Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="params.beginDocDate != null and params.beginDocDate != '' and params.endDocDate != null and params.endDocDate != ''"> and a.Doc_Date between #{params.beginDocDate} and #{params.endDocDate}</if>
            <if test="params.beginPlanDeliveryDate != null and params.beginPlanDeliveryDate != '' and params.endPlanDeliveryDate != null and params.endPlanDeliveryDate != ''"> and a.Plan_Delivery_Date between #{params.beginPlanDeliveryDate} and #{params.endPlanDeliveryDate}</if>
            <if test="params.beginDeliveryDate != null and params.beginDeliveryDate != '' and params.endDeliveryDate != null and params.endDeliveryDate != ''"> and a.Delivery_Date between #{params.beginDeliveryDate} and #{params.endDeliveryDate}</if>
            <if test="createTime != null "> and a.Create_Time = #{createTime}</if>
            <if test="createBy != null  and createBy != ''"> and a.Create_By = #{createBy}</if>
            <if test="updateTime != null "> and a.Update_Time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and a.Update_By = #{updateBy}</if>
            <if test="direction != null  and direction != ''"> and a.Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and a.Kafka_Status = #{kafkaStatus}</if>
        </where>
    </select>

    <select id="selectTblAsnOnlyById" parameterType="Long" resultMap="TblAsnResult">
        <include refid="selectTblAsnVo"/>
        where Asn_ID = #{asnId}
    </select>

    <select id="selectTblAsnFullList" parameterType="TblAsn" resultMap="TblAsnTblAsnItemApiResult">
        <include refid="selectTblAsnVo"/>
        <where>
            Direction = 'I'
            <if test="params.cursor !=null">and Asn_ID > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and Asn_ID >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and Create_Time > #{params.time}</if>
            <if test="params.timeInclude !=null">and Create_Time >= #{params.timeInclude}</if>
        </where>
        Order by Asn_ID asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>

    <insert id="insertTblAsn" parameterType="TblAsn" useGeneratedKeys="true" keyProperty="asnId">
        insert into tbl_asn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="asnCode != null and asnCode != ''">Asn_Code,</if>
            <if test="docNo != null and docNo != ''">Doc_No,</if>
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="docDate != null">Doc_Date,</if>
            <if test="planDeliveryDate != null">Plan_Delivery_Date,</if>
            <if test="deliveryDate != null">Delivery_Date,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="asnCode != null and asnCode != ''">#{asnCode},</if>
            <if test="docNo != null and docNo != ''">#{docNo},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="docDate != null">#{docDate},</if>
            <if test="planDeliveryDate != null">#{planDeliveryDate},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
         </trim>
    </insert>

    <update id="updateTblAsn" parameterType="TblAsn">
        update tbl_asn
        <trim prefix="SET" suffixOverrides=",">
            <if test="asnCode != null and asnCode != ''">Asn_Code = #{asnCode},</if>
            <if test="docNo != null and docNo != ''">Doc_No = #{docNo},</if>
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="docDate != null">Doc_Date = #{docDate},</if>
            <if test="planDeliveryDate != null">Plan_Delivery_Date = #{planDeliveryDate},</if>
            <if test="deliveryDate != null">Delivery_Date = #{deliveryDate},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status = #{kafkaStatus},</if>
        </trim>
        where Asn_ID = #{asnId}
    </update>

    <delete id="deleteTblAsnById" parameterType="Long">
        delete from tbl_asn where Asn_ID = #{asnId}
    </delete>

    <delete id="deleteTblAsnByIds" parameterType="String">
        delete from tbl_asn where Asn_ID in
        <foreach item="asnId" collection="array" open="(" separator="," close=")">
            #{asnId}
        </foreach>
    </delete>

    <select id="selectTblAsnItemList" parameterType="TblAsnItem" resultMap="TblAsnItemResult">
        <include refid="selectTblAsnItemVo"/>
        <where>
<!--            <if test="docno != null  and docno != ''"> and Doc_No = #{docNo}</if>-->
            <if test="dnNo != null  and dnNo != ''"> and Dn_No = #{dnNo}</if>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code = #{orderCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code = #{plantCode}</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="unloadingNo != null  and unloadingNo != ''"> and Unloading_No = #{unloadingNo}</if>
            <if test="unloadingName != null  and unloadingName != ''"> and Unloading_Name like concat('%', #{unloadingName}, '%')</if>
            <if test="sendLocNo != null  and sendLocNo != ''"> and Send_Loc_No = #{sendLocNo}</if>
            <if test="sendLocName != null  and sendLocName != ''"> and Send_Loc_Name like concat('%', #{sendLocName}, '%')</if>
            <if test="rcvLocNo != null  and rcvLocNo != ''"> and Rcv_Loc_No = #{rcvLocNo}</if>
            <if test="rcvLocName != null  and rcvLocName != ''"> and Rcv_Loc_Name like concat('%', #{rcvLocName}, '%')</if>
            <if test="asnId != null "> and Asn_ID = #{asnId}</if>
        </where>
    </select>

    <delete id="deleteTblAsnItemByAsnIds" parameterType="String">
        delete from tbl_asn_item where Asn_ID in
        <foreach item="asnId" collection="array" open="(" separator="," close=")">
            #{asnId}
        </foreach>
    </delete>

    <delete id="deleteTblAsnItemByAsnId" parameterType="Long">
        delete from tbl_asn_item where Asn_ID = #{asnId}
    </delete>

    <insert id="batchTblAsnItem" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="itemId" keyColumn="Item_ID">
        insert into tbl_asn_item( Doc_No, Dn_No, Order_Code, Plant_Code, Plant_Name, Unloading_No, Unloading_Name, Send_Loc_No, Send_Loc_Name, Rcv_Loc_No, Rcv_Loc_Name, Asn_ID, Create_Time, Create_By, Update_Time, Update_By) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.docNo}, #{item.dnNo}, #{item.orderCode}, #{item.plantCode}, #{item.plantName}, #{item.unloadingNo}, #{item.unloadingName}, #{item.sendLocNo}, #{item.sendLocName}, #{item.rcvLocNo}, #{item.rcvLocName}, #{item.asnId}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <insert id="batchTblAsnArticle">
        insert into tbl_asn_article( Doc_No, Article_No, Article_Name, Quantity, Unit, Batch_No, Order_Line_No, Qty_Per_Pack, Pack_Qty, Non_Std, Start_With, End_With, Item_ID, Delivery_Schedule_No, Create_Time, Create_By, Update_Time, Update_By) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.docNo}, #{item.articleNo}, #{item.articleName}, #{item.quantity}, #{item.unit}, #{item.batchNo}, #{item.orderLineNo}, #{item.qtyPerPack}, #{item.packQty}, #{item.nonStd}, #{item.startWith}, #{item.endWith}, #{item.itemId}, #{item.deliveryScheduleNo}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectLastId" resultType="Long">
        select max(Asn_ID) from tbl_asn;
    </select>

    <select id="selectTblAsnArticleList" parameterType="TblAsnArticle" resultMap="TblAsnArticleResult">
        <include refid="selectTblAsnArticleVo"/>
        <where>
            <if test="articleNo != null  and articleNo != ''"> and Article_No = #{articleNo}</if>
            <if test="articleName != null  and articleName != ''"> and Article_Name like concat('%', #{articleName}, '%')</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit = #{unit}</if>
            <if test="batchNo != null  and batchNo != ''"> and Batch_No = #{batchNo}</if>
            <if test="orderLineNo != null  and orderLineNo != ''"> and Order_Line_No = #{orderLineNo}</if>
            <if test="qtyPerPack != null "> and Qty_Per_Pack = #{qtyPerPack}</if>
            <if test="packQty != null "> and Pack_Qty = #{packQty}</if>
            <if test="nonStd != null  and nonStd != ''"> and Non_Std = #{nonStd}</if>
            <if test="startWith != null  and startWith != ''"> and Start_With = #{startWith}</if>
            <if test="endWith != null  and endWith != ''"> and End_With = #{endWith}</if>
            <if test="itemId != null "> and Item_ID = #{itemId}</if>
            <if test="deliveryScheduleNo != null  and deliveryScheduleNo != ''"> and Delivery_Schedule_No = #{deliveryScheduleNo}</if>
        </where>
    </select>

    <update id="updateOrderArticleQuantity" parameterType="TblOrderAsnQuantity">
        update tbl_order_asn_quantity set Unsent_Quantity = Unsent_Quantity + #{params.addQuantity}
        where Order_Code = #{orderCode} and Order_Line_No = #{orderLineNo} and Comp_Code = #{compCode}
            and Delivery_Schedule_No = #{deliveryScheduleNo}
    </update>

    <update id="reduceOrderArticleQuantityByAsnId" parameterType="Long">
        update tbl_order_asn_quantity m
        set Unsent_Quantity = Unsent_Quantity + (select Quantity
                                                 from (select a.Comp_Code, b.Order_Code, c.Order_Line_No, c.Quantity, c.Delivery_Schedule_No
                                                       from tbl_asn a,
                                                            tbl_asn_item b,
                                                            tbl_asn_article c
                                                       where a.Asn_ID = b.Asn_ID
                                                         and b.Item_ID = c.Item_ID
                                                         and a.Asn_ID = #{asnId}) n
                                                 where n.Comp_Code = m.Comp_Code
                                                   and n.Order_Code = m.Order_Code
                                                   and n.Order_Line_No = m.Order_Line_No
                                                   and n.Delivery_Schedule_No = m.Delivery_Schedule_No)
        where (m.Order_Code,m.Order_Line_No,m.Delivery_Schedule_No) in (
            select a.Order_Code, b.Order_Line_No, b.Delivery_Schedule_No
            from tbl_asn_item a, tbl_asn_article b
            where a.Item_ID = b.Item_ID
              and a.Asn_ID = #{asnId}
        );
    </update>

    <update id="addOrderArticleQuantityByAsnId" parameterType="Long">
        update tbl_order_asn_quantity m
        set Unsent_Quantity = Unsent_Quantity - (select Quantity
                                                 from (select a.Comp_Code, b.Order_Code, c.Order_Line_No, c.Quantity, c.Delivery_Schedule_No
                                                       from tbl_asn a,
                                                            tbl_asn_item b,
                                                            tbl_asn_article c
                                                       where a.Asn_ID = b.Asn_ID
                                                         and b.Item_ID = c.Item_ID
                                                         and a.Asn_ID = #{asnId}) n
                                                 where n.Comp_Code = m.Comp_Code
                                                   and n.Order_Code = m.Order_Code
                                                   and n.Order_Line_No = m.Order_Line_No
                                                   and n.Delivery_Schedule_No = m.Delivery_Schedule_No)
        where (m.Order_Code,m.Order_Line_No,m.Delivery_Schedule_No) in (
            select a.Order_Code, b.Order_Line_No, b.Delivery_Schedule_No
            from tbl_asn_item a, tbl_asn_article b
            where a.Item_ID = b.Item_ID
              and a.Asn_ID = #{asnId}
        );
    </update>

    <update id="recoverOrderArticleQuantityByAsnId" parameterType="Long">
        update tbl_order_asn_quantity m
        set Unsent_Quantity = Unsent_Quantity + (select Quantity
                                                 from (select a.Comp_Code, b.Order_Code, c.Order_Line_No, c.Quantity, c.Delivery_Schedule_No
                                                       from tbl_asn a,
                                                            tbl_asn_item b,
                                                            tbl_asn_article c
                                                       where a.Asn_ID = b.Asn_ID
                                                         and b.Item_ID = c.Item_ID
                                                         and a.Asn_ID = #{asnId}) n
                                                 where n.Comp_Code = m.Comp_Code
                                                   and n.Order_Code = m.Order_Code
                                                   and n.Order_Line_No = m.Order_Line_No
                                                   and n.Delivery_Schedule_No = m.Delivery_Schedule_No)
        where (m.Order_Code,m.Order_Line_No,m.Delivery_Schedule_No) in (
            select a.Order_Code, b.Order_Line_No, b.Delivery_Schedule_No
            from tbl_asn_item a, tbl_asn_article b
            where a.Item_ID = b.Item_ID
              and a.Asn_ID = #{asnId}
        );
    </update>

    <delete id="deleteTblAsnArticleByAsnId" parameterType="Long">
        delete from tbl_asn_article where Item_ID in (select Item_ID from tbl_asn_item where Asn_ID  = #{asnId})
    </delete>

    <resultMap id="tblOrderAsnQuantityMap" type="TblOrderAsnQuantity">
        <result property="orderCode" column="Order_Code"/>
        <result property="orderLineNo" column="Order_Line_No"/>
        <result property="deliveryScheduleNo" column="Delivery_Schedule_No"/>
        <result property="compCode" column="Comp_Code"/>
        <result property="quantity" column="Quantity"/>
        <result property="unsentQuantity" column="Unsent_Quantity"/>
    </resultMap>

    <select id="selectTblOrderAsnQuantityList" parameterType="TblOrderAsnQuantity" resultMap="tblOrderAsnQuantityMap">
        select Order_Code, Order_Line_No, Delivery_Schedule_No, Comp_Code, Quantity, Unsent_Quantity from tbl_order_asn_quantity
        <where>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code = #{orderCode}</if>
            <if test="orderLineNo != null  and orderLineNo != ''"> and Order_Line_No = #{orderLineNo}</if>
            <if test="deliveryScheduleNo != null  and deliveryScheduleNo != ''"> and Delivery_Schedule_No = #{deliveryScheduleNo}</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code = #{compCode}</if>
        </where>
    </select>

    <select id="selectTblAsnForPrint" parameterType="TblAsn" resultMap="TblAsnForPrintResult">
        select
            a.Asn_ID, a.Asn_Code, a.Doc_No, a.Comp_Code, a.Supp_Code,
            COALESCE(p.dept_name, a.Supp_Name) as Supp_Name,
            a.Doc_Date, a.Plan_Delivery_Date, a.Delivery_Date, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status,
            b.Item_ID as sub_Item_ID, b.Doc_No as sub_Doc_No, b.Dn_No as sub_Dn_No, b.Order_Code as sub_Order_Code,
            b.Plant_Code as sub_Plant_Code, b.Plant_Name as sub_Plant_Name, b.Unloading_No as sub_Unloading_No,
            b.Unloading_Name as sub_Unloading_Name, b.Send_Loc_No as sub_Send_Loc_No, b.Send_Loc_Name as sub_Send_Loc_Name,
            b.Rcv_Loc_No as sub_Rcv_Loc_No, b.Rcv_Loc_Name as sub_Rcv_Loc_Name, b.Asn_ID as sub_Asn_ID,
            b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By,
            c.Article_ID as sub_sub_Article_ID, c.Doc_No as sub_sub_Doc_No, c.Dn_No as sub_sub_Dn_No,
            c.Article_No as sub_sub_Article_No, COALESCE(m.Material_Name, c.Article_Name) as sub_sub_Article_Name,
            c.Quantity as sub_sub_Quantity, c.Unit as sub_sub_Unit, c.Batch_No as sub_sub_Batch_No,
            c.Order_Line_No as sub_sub_Order_Line_No, c.Qty_Per_Pack as sub_sub_Qty_Per_Pack,
            c.Pack_Qty as sub_sub_Pack_Qty, c.Non_Std as sub_sub_Non_Std, c.Start_With as sub_sub_Start_With,
            c.End_With as sub_sub_End_With, c.Item_ID as sub_sub_Item_ID, c.Delivery_Schedule_No as sub_sub_Delivery_Schedule_No,
            c.Create_Time as sub_sub_Create_Time, c.Create_By as sub_sub_Create_By,
            c.Update_Time as sub_sub_Update_Time, c.Update_By as sub_sub_Update_By
        from tbl_asn a
        left join tbl_asn_item b on b.Asn_ID = a.Asn_ID
        left join tbl_asn_article c on c.Item_ID = b.Item_ID
        left join sys_dept p on a.Supp_Code = p.supplier_code
        left join tbl_material m on c.Article_No = m.Material_Code and m.Del_Flag = '0'
        where a.Asn_ID = #{asnId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
</mapper>
