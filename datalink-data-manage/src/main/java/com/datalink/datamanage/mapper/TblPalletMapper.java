package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.TblPallet;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 托盘信息Mapper接口
 */
@Mapper
public interface TblPalletMapper {
    /**
     * 查询托盘信息
     *
     * @param palletId 托盘信息ID
     * @return 托盘信息
     */
    public TblPallet selectTblPalletById(Long palletId);

    /**
     * 查询托盘信息列表
     *
     * @param tblPallet 托盘信息
     * @return 托盘信息集合
     */
    public List<PalletInfoVO> selectTblPalletList(PalletInfoVO tblPallet);
    
    /**
     * 查询托盘信息视图列表
     *
     * @param palletInfoVO 托盘信息视图
     * @return 托盘信息视图集合
     */
    public List<PalletInfoVO> selectPalletInfoList(PalletInfoVO palletInfoVO);

    /**
     * 根据物料工厂ID查询托盘信息
     *
     * @param materialPlantId 物料工厂ID
     * @return 托盘信息
     */
    public TblPallet selectTblPalletByMaterialPlantId(Long materialPlantId);

    /**
     * 新增托盘信息
     *
     * @param tblPallet 托盘信息
     * @return 结果
     */
    public int insertTblPallet(TblPallet tblPallet);

    /**
     * 修改托盘信息
     *
     * @param tblPallet 托盘信息
     * @return 结果
     */
    public int updateTblPallet(TblPallet tblPallet);

    /**
     * 删除托盘信息
     *
     * @param palletId 托盘信息ID
     * @return 结果
     */
    public int deleteTblPalletById(Long palletId);

    /**
     * 批量删除托盘信息
     *
     * @param palletIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblPalletByIds(Long[] palletIds);
} 