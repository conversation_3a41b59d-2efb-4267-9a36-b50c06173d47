package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 托盘信息对象 tbl_pallet
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TblPallet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 托盘ID */
    private Long palletId;

    /** 物料工厂ID */
    @ApiModelProperty("物料工厂ID")
    private Long materialPlantId;

    /** 整托包含SNP数量 */
    @Excel(name = "整托包含SNP数量")
    @ApiModelProperty("整托包含SNP数量")
    private Integer snpQuantity;

    /** 托长(mm) */
    @Excel(name = "托长(mm)")
    @ApiModelProperty("托长")
    private Integer palletLength;

    /** 托宽(mm) */
    @Excel(name = "托宽(mm)")
    @ApiModelProperty("托宽")
    private Integer palletWidth;

    /** 托高(mm) */
    @Excel(name = "托高(mm)")
    @ApiModelProperty("托高")
    private Integer palletHeight;

    /** 容器种类 */
    @Excel(name = "容器种类")
    @ApiModelProperty("容器种类")
    private String containerType;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
    
    /** 物料工厂信息 */
    private TblMaterialPlant materialPlant;
} 