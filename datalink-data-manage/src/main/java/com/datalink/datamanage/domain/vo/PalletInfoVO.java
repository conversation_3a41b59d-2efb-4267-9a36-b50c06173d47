package com.datalink.datamanage.domain.vo;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 托盘信息视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PalletInfoVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 托盘ID */
    private Long palletId;

    /** 物料工厂ID */
    private Long materialPlantId;

    /** 物料ID */
    private Long materialId;

    /** 物料编号 */
    @Excel(name = "物料编号")
    @ApiModelProperty("零件号")
    private String materialCode;

    /** 物料描述 */
    @Excel(name = "物料描述")
    @ApiModelProperty("零件描述")
    private String materialName;

    /** 基本计量单位 */
    @Excel(name = "基本计量单位")
    private String baseUnit;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    @ApiModelProperty("工厂代码")
    private String plantCode;
    
    /** 包装数量 */
    @Excel(name = "包装数量")
    @ApiModelProperty("单位包装数")
    private Long packQuantity;

    /** 整托包含SNP数量 */
    @Excel(name = "整托包含SNP数量")
    @ApiModelProperty("整托包含SNP数量")
    private Integer snpQuantity;

    /** 托长(mm) */
    @Excel(name = "托长(mm)")
    @ApiModelProperty("托长")
    private Integer palletLength;

    /** 托宽(mm) */
    @Excel(name = "托宽(mm)")
    @ApiModelProperty("托宽")
    private Integer palletWidth;

    /** 托高(mm) */
    @Excel(name = "托高(mm)")
    @ApiModelProperty("托高")
    private Integer palletHeight;

    /** 容器种类 */
    @Excel(name = "容器种类")
    @ApiModelProperty("容器种类")
    private String containerType;
} 