package com.datalink.datamanage.controller;

import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblPallet;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import com.datalink.datamanage.service.ITblPalletService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 托盘维护Controller
 */
@Api(tags = "托盘维护")
@RestController
@RequestMapping("/datamanage/pallet")
public class TblPalletController extends BaseController {
    @Autowired
    private ITblPalletService tblPalletService;

    /**
     * 查询托盘信息列表
     */
    @ApiOperation("查询托盘信息列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = PalletInfoVO.class)
    })
    @PreAuthorize("@ss.hasPermi('datamanage:pallet:list')")
    @GetMapping("/list")
    public TableDataInfo list(PalletInfoVO palletInfoVO) {
        startPage();
        List<PalletInfoVO> list = tblPalletService.selectPalletInfoList(palletInfoVO);
        return getDataTable(list);
    }

    /**
     * 导出托盘信息列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:pallet:export')")
    @Log(title = "托盘信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(PalletInfoVO palletInfoVO) {
        List<PalletInfoVO> list = tblPalletService.selectPalletInfoList(palletInfoVO);
        ExcelUtil<PalletInfoVO> util = new ExcelUtil<PalletInfoVO>(PalletInfoVO.class);
        return util.exportExcel(list, "托盘信息数据");
    }

    /**
     * 获取托盘信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:pallet:query')")
    @GetMapping(value = "/{palletId}")
    public AjaxResult getInfo(@PathVariable("palletId") Long palletId) {
        return AjaxResult.success(tblPalletService.selectTblPalletById(palletId));
    }

    /**
     * 新增或修改托盘信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:pallet:edit')")
    @Log(title = "托盘信息", businessType = BusinessType.UPDATE)
    @ApiOperation("新增或修改托盘信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tblPallet", value = "托盘信息", required = true, dataType = "TblPallet")
    })
    @PostMapping("/save")
    public AjaxResult save(@RequestBody TblPallet tblPallet) {
        try {
            // 根据是否有palletId判断是新增还是修改
            if (tblPallet.getPalletId() == null) {
                // 新增
                return toAjax(tblPalletService.insertTblPallet(tblPallet));
            } else {
                // 修改
                return toAjax(tblPalletService.updateTblPallet(tblPallet));
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除托盘信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:pallet:remove')")
    @Log(title = "托盘信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{palletIds}")
    public AjaxResult remove(@PathVariable Long[] palletIds) {
        return toAjax(tblPalletService.deleteTblPalletByIds(palletIds));
    }
} 