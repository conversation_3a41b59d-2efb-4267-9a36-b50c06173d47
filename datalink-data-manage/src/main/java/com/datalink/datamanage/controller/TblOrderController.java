package com.datalink.datamanage.controller;

import com.datalink.common.DataConstants;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.service.ITblOrderService;
import com.datalink.framework.web.service.PermissionService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.util.*;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
@Api(tags = "订单管理")
@RestController
@RequestMapping("/datamanage/order")
public class TblOrderController extends BaseController
{
    @Autowired
    private ITblOrderService tblOrderService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 查询订单列表
     */
//    @ApiOperation("订单列表查询")
    @ApiImplicitParam(name = "order", value = "查询参数", dataType = "TblOrder")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = TblOrder.class)
    })
    @PreAuthorize("@ss.hasPermi('datamanage:order:list')")
    @DataScope(supplierAlias = "a")
//    @GetMapping("/list")
    public TableDataInfo list(TblOrder tblOrder, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        startPage();
        List<TblOrder> list = tblOrderService.selectValidTblOrderList(tblOrder, tz);
        return getDataTable(list);
    }

    /**
     * 查询flatOrder列表
     */
    @ApiOperation("订单列表查询")
    @ApiImplicitParam(name = "flatOrder", value = "查询参数", dataType = "FlatOrder")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = FlatOrder.class)
    })
    @PreAuthorize("@ss.hasPermi('datamanage:order:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(FlatOrder flatOrder, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        startPage();
        List<FlatOrder> list = tblOrderService.selectFlatOrderList(flatOrder, tz);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @GetMapping("/export/{orderId}")
    public AjaxResult export(@PathVariable("orderId") Long orderId){
        TblOrder order = tblOrderService.selectTblOrderById(orderId);
        InputStream template = this.getClass().getClassLoader().getResourceAsStream("templates/order.xlsx");
        Locale locale = LocaleContextHolder.getLocale();
        if (locale.equals(Locale.CHINESE)) {
            template = this.getClass().getClassLoader().getResourceAsStream("templates/order.xlsx");
        } else if (locale.equals(Locale.ENGLISH)) {
            template = this.getClass().getClassLoader().getResourceAsStream("templates/order_en.xlsx");
        } else if (locale.equals(Locale.JAPANESE)) {
            template = this.getClass().getClassLoader().getResourceAsStream("templates/order_ja.xlsx");
        }
        ExcelUtil<TblOrder> util = new ExcelUtil<TblOrder>(TblOrder.class);
        return util.exportByTemplate(order.buildHeaderMap(), order.getDetail(), template, "Order_"+order.getOrderCode());
    }
//    public AjaxResult export(TblOrder tblOrder)
//    {
//        List<TblOrder> list = tblOrderService.selectTblOrderList(tblOrder);
//        ExcelUtil<TblOrder> util = new ExcelUtil<TblOrder>(TblOrder.class);
//        return util.exportExcel(list, "订单数据");
//    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return AjaxResult.success(tblOrderService.selectTblOrderById(orderId));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblOrder tblOrder)
    {
        return toAjax(tblOrderService.insertTblOrder(tblOrder));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblOrder tblOrder)
    {
        return toAjax(tblOrderService.updateTblOrder(tblOrder));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(tblOrderService.deleteTblOrderByIds(orderIds));
    }

    /**
     * 获取订单行项目
     */
    @ApiOperation("订单行项目列表查询")
    @ApiImplicitParam(name = "tblOrderItem", value = "查询参数", dataType = "TblOrderItem")
    @PreAuthorize("@ss.hasPermi('datamanage:order:query')")
    @GetMapping(value = "/listItems")
    public TableDataInfo listItems(TblOrderItem tblOrderItem)
    {
        startPage();
        List<TblOrderItem> list = tblOrderService.selectTblOrderItemList(tblOrderItem);
//        // 获取当前登录用户
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        // 判断是否包含没有价格权限的供应商角色
//        if (permissionService.hasRole("noPriceSupplier") && !loginUser.getUser().isAdmin()) {
//            // 隐藏采购价格，将netPrice设置为null，priceUnit设置为null
//            list.forEach(item -> {
//                item.setNetPrice(null);
//                item.setPriceUnit("");
//            });
//        }
        return getDataTable(list);
    }

    /**
     * 获取订单详细信息(不包含行项目)
     */
    @ApiOperation("获取订单详细信息(不包含行项目)")
    @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, dataType = "Long")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = TblOrder.class)
    })
    @PreAuthorize("@ss.hasPermi('datamanage:order:query')")
    @GetMapping(value = "/head/{orderId}")
    public AjaxResult getOrderOnly(@PathVariable("orderId") Long orderId)
    {
        return AjaxResult.success(tblOrderService.selectTblOrderOnlyById(orderId));
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "订单ASN模版", businessType = BusinessType.EXPORT)
    @GetMapping("/exportAsnTemp/{orderIds}")
    public AjaxResult exportAsnTemp(@PathVariable("orderIds") Long[] orderIds){
        Map<String, Object> header = new HashMap<>();
        List<TblAsnItem> items = new ArrayList<>();
        List<ExcelAsnArticle> articles = new ArrayList<>();
        for (Long orderId : orderIds){
            TblOrder order = tblOrderService.selectTblOrderById(orderId);
            header.put("compCode", order.getCompCode());
            header.put("suppCode", order.getSuppCode());
            header.put("suppName", order.getSuppName());
            TblAsnItem item = new TblAsnItem();
            item.setOrderCode(order.getOrderCode());
            item.setPlantCode(order.getPlantCode());
            item.setPlantName(order.getPlantName());
            items.add(item);
            for (TblOrderItem orderItem : order.getDetail()){
                ExcelAsnArticle article = new ExcelAsnArticle();
                article.setOrderCodeSub(order.getOrderCode());
                article.setArticleNo(orderItem.getArticleNo());
                article.setArticleName(orderItem.getArticleName());
                article.setOrderLineNo(orderItem.getItemNo());
                article.setQuantity(orderItem.getQuantity());
                article.setUnit(orderItem.getUnit());
                article.setQtyPerPack(orderItem.getQtyPerPack());
                articles.add(article);
            }
        }
        Map<String, List> data = new HashMap<>();
        data.put("item", items);
        data.put("article", articles);

        InputStream template = this.getClass().getClassLoader().getResourceAsStream("templates/asnOrderTemp.xlsx");
        ExcelUtil<TblOrder> util = new ExcelUtil<TblOrder>(TblOrder.class);
        return util.exportMultiTableByTemplate(header, data, template, "ASN_Template");
    }

    /**
     * 打印订单列表pdf
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "打印现品票pdf", businessType = BusinessType.EXPORT)
    @PostMapping("/printOrderPdf")
    public AjaxResult printOrderPdf(@RequestBody List<Long> orderIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz){
        return tblOrderService.printOrderPdf(orderIds, tz);
    }

    /**
     * 打印订单列表pdf
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "打印部品纳入指示书pdf", businessType = BusinessType.EXPORT)
    @PostMapping("/printInstructPdf")
    public AjaxResult printInstructPdf(@RequestBody List<Long> orderIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz){
        return tblOrderService.printInstructPdf(orderIds, tz);
    }

    /**
     * 打印picking list pdf
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "打印picking list", businessType = BusinessType.EXPORT)
    @PostMapping("/printPickingList")
    public AjaxResult printPickingList(@RequestBody List<Long> orderIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz){
        return tblOrderService.printPickingList(orderIds, tz);
    }

    /**
     * 打印订单列表pdf
     */
    @PreAuthorize("@ss.hasPermi('datamanage:order:export')")
    @Log(title = "打印订单Txt", businessType = BusinessType.EXPORT)
    @PostMapping("/printOrderTxt")
    public AjaxResult printOrderTxt(@RequestBody List<Long> orderIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz){
        return tblOrderService. printOrderTxt(orderIds, tz);
    }

    /**
     * 订单确认
     */
    @ApiOperation("订单确认")
    @ApiImplicitParam(name = "orderIds", value = "订单ID列表", dataType = "List<Long>", allowMultiple = true)
    @PreAuthorize("@ss.hasPermi('datamanage:order:confirm')")
    @Log(title = "订单确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirm(@RequestBody List<Long> orderIds)
    {
        return toAjax(tblOrderService.confirmOrders(orderIds));
    }

    /**
     * 下载结算单Excel
     */
    @ApiOperation("下载订单Excel")
    @ApiImplicitParam(name = "orderIds", value = "订单ID列表", dataType = "List<Long>", allowMultiple = true)
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:export')")
    @Log(title = "订单Excel", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a")
    @PostMapping("/downloadExcel")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult downloadExcel(@RequestBody List<Long> orderIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        List<TblOrder> list = new ArrayList<>(orderIds.size());
        for (Long orderId : orderIds) {
            TblOrder order = tblOrderService.selectTblOrderById(orderId);
            if (order != null) {
                list.add(order);
                tblOrderService.updateTblOrderOnly(new TblOrder(){{
                    setOrderId(orderId);
                    setDownloadStatus(DataConstants.ORDER_STATUS_DOWNLOADED);
                    setUpdateTime(new Date());
                    setUpdateBy(SecurityUtils.getUsername());
                }});
            }
        }
        ExcelUtil<TblOrder> util = new ExcelUtil<TblOrder>(TblOrder.class);
        return util.exportExcelFlat(list, "采购订单");
    }
}
