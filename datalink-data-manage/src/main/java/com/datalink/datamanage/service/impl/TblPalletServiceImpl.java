package com.datalink.datamanage.service.impl;

import java.util.List;
import java.util.Date;
import com.datalink.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.datalink.datamanage.mapper.TblPalletMapper;
import com.datalink.datamanage.domain.TblPallet;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import com.datalink.datamanage.service.ITblPalletService;

/**
 * 托盘信息Service业务层处理
 */
@Service
public class TblPalletServiceImpl implements ITblPalletService {
    @Autowired
    private TblPalletMapper tblPalletMapper;

    /**
     * 查询托盘信息
     *
     * @param palletId 托盘信息ID
     * @return 托盘信息
     */
    @Override
    public TblPallet selectTblPalletById(Long palletId) {
        return tblPalletMapper.selectTblPalletById(palletId);
    }

    /**
     * 查询托盘信息列表
     *
     * @param tblPallet 托盘信息
     * @return 托盘信息
     */
    @Override
    public List<PalletInfoVO> selectTblPalletList(PalletInfoVO tblPallet) {
        return tblPalletMapper.selectTblPalletList(tblPallet);
    }
    
    /**
     * 查询托盘信息视图列表
     *
     * @param palletInfoVO 托盘信息视图
     * @return 托盘信息视图集合
     */
    @Override
    public List<PalletInfoVO> selectPalletInfoList(PalletInfoVO palletInfoVO) {
        return tblPalletMapper.selectPalletInfoList(palletInfoVO);
    }

    /**
     * 根据物料工厂ID查询托盘信息
     *
     * @param materialPlantId 物料工厂ID
     * @return 托盘信息
     */
    @Override
    public TblPallet selectTblPalletByMaterialPlantId(Long materialPlantId) {
        return tblPalletMapper.selectTblPalletByMaterialPlantId(materialPlantId);
    }

    /**
     * 新增托盘信息
     *
     * @param tblPallet 托盘信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTblPallet(TblPallet tblPallet) {
        // 检查是否已存在相同物料工厂的托盘信息
        TblPallet existPallet = tblPalletMapper.selectTblPalletByMaterialPlantId(tblPallet.getMaterialPlantId());
        if (existPallet != null) {
            throw new RuntimeException("该物料工厂已存在托盘信息");
        }
        
        tblPallet.setCreateTime(new Date());
        tblPallet.setCreateBy(SecurityUtils.getUsername());
        return tblPalletMapper.insertTblPallet(tblPallet);
    }

    /**
     * 修改托盘信息
     *
     * @param tblPallet 托盘信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTblPallet(TblPallet tblPallet) {
        // 如果修改了物料工厂ID，需要检查是否与其他托盘信息冲突
        TblPallet oldPallet = tblPalletMapper.selectTblPalletById(tblPallet.getPalletId());
        if (oldPallet != null && !oldPallet.getMaterialPlantId().equals(tblPallet.getMaterialPlantId())) {
            TblPallet existPallet = tblPalletMapper.selectTblPalletByMaterialPlantId(tblPallet.getMaterialPlantId());
            if (existPallet != null) {
                throw new RuntimeException("该物料工厂已存在托盘信息");
            }
        }
        
        tblPallet.setUpdateTime(new Date());
        tblPallet.setUpdateBy(SecurityUtils.getUsername());
        return tblPalletMapper.updateTblPallet(tblPallet);
    }

    /**
     * 批量删除托盘信息
     *
     * @param palletIds 需要删除的托盘信息ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTblPalletByIds(Long[] palletIds) {
        return tblPalletMapper.deleteTblPalletByIds(palletIds);
    }

    /**
     * 删除托盘信息信息
     *
     * @param palletId 托盘信息ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTblPalletById(Long palletId) {
        return tblPalletMapper.deleteTblPalletById(palletId);
    }
} 