package com.datalink.datamanage.service.impl;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.SapAsnItem;
import com.datalink.api.domain.SapListRequst;
import com.datalink.common.DataConstants;
import com.datalink.common.Util;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.*;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import com.datalink.datamanage.mapper.TblAsnMapper;
import com.datalink.datamanage.service.ITblAsnService;
import com.datalink.datamanage.service.ITblMaterialService;
import com.datalink.datamanage.service.ITblOrderService;
import com.datalink.datamanage.service.ITblPalletService;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.system.domain.SysConfig;
import com.datalink.system.service.ISysConfigService;
import com.datalink.system.service.ISysDictDataService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ASNService业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
@Service
public class TblAsnServiceImpl implements ITblAsnService
{
    private static final Logger log = LoggerFactory.getLogger(TblAsnServiceImpl.class);
    @Autowired
    private TblAsnMapper tblAsnMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ITblOrderService orderService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private SapApiClient sapApiClient;

    @Autowired
    private ITblMaterialService tblMaterialService;

    @Autowired
    private ITblPalletService tblPalletService;


    @Value("${file.jasper-template-dir}")
    private String templatePath;

    /**
     * 查询ASN
     *
     * @param asnId ASNID
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnById(Long asnId)
    {
        TblAsn param = new TblAsn();
        param.setAsnId(asnId);
        return tblAsnMapper.selectTblAsnById(param);
    }

    /**
     * 查询ASN
     *
     * @param asnCode ASNCode
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnByAsnCode(String asnCode)
    {
        TblAsn param = new TblAsn();
        param.setAsnCode(asnCode);
        TblAsn asn = tblAsnMapper.selectTblAsnByAsnCode(param);
        if(asn == null) {
            throw new RuntimeException("ASN不存在，ASNCode: " + asnCode);
        }
        for (TblAsnItem item : asn.getDetail()) {
            for (TblAsnArticle article : item.getArticles()) {
                // 根据物料和托盘配置组装托盘信息
                BigDecimal boxSnp = BigDecimal.ONE;
                TblMaterial tblMaterial = tblMaterialService.selectTblMaterialByCodeAndPlant(article.getArticleNo(), item.getPlantCode());
                if (tblMaterial != null) {
                    boxSnp = tblMaterial.getPlants().stream()
                            .findFirst()
                            .map(TblMaterialPlant::getPackQuantity).filter(qty -> qty.compareTo(BigDecimal.ZERO) != 0).orElse(BigDecimal.ONE);
                }
                PalletInfoVO palletInfoVO = new PalletInfoVO();
                palletInfoVO.setPlantCode(item.getPlantCode());
                palletInfoVO.setMaterialCode(article.getArticleNo());
                List<PalletInfoVO> palletInfoVOS = tblPalletService.selectTblPalletList(palletInfoVO);
                List<TblAsnArticle.Pallet> pallets = new ArrayList<>();
                if (!palletInfoVOS.isEmpty()) {
                    // 有托盘配置则这个article有多个托盘，每个托盘label唯一，box.qty根据托盘数量/snp计算
                    if (palletInfoVOS.size() > 1) {
                        throw new RuntimeException("物料" + article.getArticleNo() +"配置了多个托盘，请检查物料配置");
                    }
                    PalletInfoVO palletInfo = palletInfoVOS.get(0);
                    Integer palletSnp = palletInfo.getSnpQuantity();
                    BigDecimal palletSize = article.getQuantity().divide(new BigDecimal(palletSnp), 0, RoundingMode.UP);
                    for (int i = 1; i <= palletSize.intValue(); i++) {
                        TblAsnArticle.Pallet pallet = new TblAsnArticle.Pallet();
                        BigDecimal subtract = article.getQuantity().subtract(new BigDecimal(palletSnp).multiply(new BigDecimal(i - 1)));
                        // 剩余数量小于托盘snp则托盘数量为剩余数量，否则为托盘snp数量
                        BigDecimal palletQty = subtract.compareTo(new BigDecimal(palletSnp)) < 0 ? subtract : new BigDecimal(palletSnp);
                        // 托盘标签根据asnCode+orderCode+itemNo+deliveryScheduleNo+palletIndex+palletQty生成
                        pallet.setLabel("ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
                                + "%RELEASE@" + article.getDeliveryScheduleNo() + "%BATCH@" + article.getBatchNo() + "%PALLET@" + i + "%PQTY@" + palletQty + "%");
                        List<TblAsnArticle.Box> boxes = new ArrayList<>();
                        BigDecimal boxSize = palletQty.divide(boxSnp, 0, RoundingMode.UP);
                        for (int j = 1; j <= boxSize.intValue(); j++) {
                            TblAsnArticle.Box box = new TblAsnArticle.Box();
                            BigDecimal boxSubtract = palletQty.subtract(boxSnp.multiply(new BigDecimal(j - 1)));
                            box.setQty(boxSubtract.compareTo(boxSnp) < 0 ? boxSubtract : boxSnp);
                            // 箱标签根据asnCode+orderCode+itemNo+deliveryScheduleNo+palletIndex+boxIndex+boxQty生成
                            box.setLabel("ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
                                    + "%RELEASE@" + article.getDeliveryScheduleNo() + "%BATCH@" + article.getBatchNo() + "%PALLET@" + i + "%BOX@" + j + "%BQTY@" + box.getQty() + "%");
                            boxes.add(box);
                        }
                        pallet.setBox(boxes);
                        pallets.add(pallet);
                    }
                } else {
                    // 没有托盘配置则这个article只有一个托盘，托盘label为空，box.qty根据article数量/snp计算
                    TblAsnArticle.Pallet pallet = new TblAsnArticle.Pallet();
                    pallet.setLabel("");
                    List<TblAsnArticle.Box> boxes = new ArrayList<>();
                    BigDecimal boxSize = article.getQuantity().divide(boxSnp, 0, RoundingMode.UP);
                    for (int i = 1; i <= boxSize.intValue(); i++) {
                        TblAsnArticle.Box box = new TblAsnArticle.Box();
                        BigDecimal subtract = article.getQuantity().subtract(boxSnp.multiply(new BigDecimal(i - 1)));
                        box.setQty(subtract.compareTo(boxSnp) < 0 ? subtract : boxSnp);
                        // 箱标签根据asnCode+orderCode+itemNo+deliveryScheduleNo+palletIndex+boxIndex+boxQty生成
                        box.setLabel("ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
                                + "%RELEASE@" + article.getDeliveryScheduleNo() + "%BATCH@" + article.getBatchNo() + "%PALLET@" + "%BOX@" + i + "%BQTY@" + box.getQty() + "%");
                        boxes.add(box);
                    }
                    pallet.setBox(boxes);
                    pallets.add(pallet);
                }
                article.setPallet(pallets);
            }
        }
        return asn;
    }

    /**
     * 查询ASN列表
     *
     * @param tblAsn ASN
     * @return ASN
     */
    @Override
    public List<TblAsn> selectTblAsnList(TblAsn tblAsn)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的岗位编码
            String userName = loginUser.getUser().getUserName();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            tblAsn.setCreateBy(userName);
        }
        // 试制工厂（223X）员工只能看到试制工厂相关的asn
        boolean is223X = false;
        if (permissionService.hasRole("223Xuser") && !loginUser.getUser().isAdmin()) {
            is223X = true;
        }
        return tblAsnMapper.selectTblAsnList(tblAsn, is223X);
    }

    /**
     * 查询ASN及行项目列表
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    @Override
    public List<TblAsn> selectTblAsnWithItemList(TblAsn tblAsn) {
        return tblAsnMapper.selectTblAsnWithItemList(tblAsn);
    }

    /**
     * 新增ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblAsn(TblAsn tblAsn)
    {
        if (null == tblAsn.getAsnCode()){
            genAsnCode(tblAsn);
//            if (tblAsn.getDirection().equals(DataConstants.DIRECTION_OUT) && tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_TO_SEND)){
//                genBarCode(tblAsn);
//            }
        }
        tblAsn.setCreateTime(DateUtils.getNowDate());
        for (TblAsnItem item : tblAsn.getDetail()) {
            // 校验订单状态
            Map<String, Boolean> orderStatus = orderService.checkOrderStatus(item.getOrderCode());
            // 校验订单已完成时不允许新增
            if (orderStatus.get("isComplete")) {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("order.is.complete"), item.getOrderCode()));
            }
            // 校验只有确认后的订单才能新增ASN
            if (!orderStatus.get("isConfirmed")) {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("order.is.not.confirm"), item.getOrderCode()));
            }
        }
        int rows = tblAsnMapper.insertTblAsn(tblAsn);
        insertTblAsnItem(tblAsn);
        updateOrderCompleteStatus(tblAsn);
        if (!tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            sendToSap(tblAsn);
        }

        return rows;
    }

    private void updateOrderCompleteStatus(TblAsn tblAsn) {
        for (TblAsnItem item : tblAsn.getDetail()) {
            orderService.updateOrderCompleteStatus(item.getOrderCode());
        }
    }

    /**
     * 修改ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblAsn(TblAsn tblAsn)
    {
//        if (tblAsn.getDirection().equals(DataConstants.DIRECTION_OUT) && tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_TO_SEND)){
//            genBarCode(tblAsn);
//        }
        if(tblAsn.getAsnId() == null){
            throw new RuntimeException("asnId不能为空");
        }
        TblAsn asn = tblAsnMapper.selectTblAsnById(tblAsn);
        if (asn == null){
            // 防止将已删除的ASN送到SAP
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.not.exists"), tblAsn.getAsnCode()));
        }
        // 判断是否已发送，防止重复提交
        if (DataConstants.KAFKA_STATUS_TO_SEND.equals(asn.getKafkaStatus())) {
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.is.sent"), tblAsn.getAsnCode()));
        }
        tblAsn.setUpdateTime(DateUtils.getNowDate());
        tblAsnMapper.reduceOrderArticleQuantityByAsnId(tblAsn.getAsnId());
        tblAsnMapper.deleteTblAsnArticleByAsnId(tblAsn.getAsnId());
        tblAsnMapper.deleteTblAsnItemByAsnId(tblAsn.getAsnId());
        insertTblAsnItem(tblAsn);
        updateOrderCompleteStatus(tblAsn);
        if (!tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            sendToSap(tblAsn);
        }
        return tblAsnMapper.updateTblAsn(tblAsn);
    }

    /**
     * 修改ASN(不包含行项目)
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Override
    public int updateTblAsnOnly(TblAsn tblAsn)
    {
        tblAsn.setUpdateTime(DateUtils.getNowDate());
        return tblAsnMapper.updateTblAsn(tblAsn);
    }

    /**
     * 批量删除ASN
     *
     * @param asnIds 需要删除的ASNID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblAsnByIds(Long[] asnIds)
    {
        for (Long asnId : asnIds){
            TblAsn asn = tblAsnMapper.selectTblAsnOnlyById(asnId);
            if (asn != null && DataConstants.KAFKA_STATUS_TO_SEND.equals(asn.getKafkaStatus())) {
                throw new RuntimeException(asn.getAsnCode() + "已发送，不允许删除");
            }
            tblAsnMapper.reduceOrderArticleQuantityByAsnId(asnId);
            List<TblAsnItem> itemList = tblAsnMapper.selectTblAsnItemByAsnId(asnId);
            // 若unsent_quantity大于0，则将order.is_complete设为N
            for (TblAsnItem item : itemList){
                orderService.updateOrderCompleteStatus(item.getOrderCode());
            }
            tblAsnMapper.deleteTblAsnArticleByAsnId(asnId);
        }

        tblAsnMapper.deleteTblAsnItemByAsnIds(asnIds);
        return tblAsnMapper.deleteTblAsnByIds(asnIds);
    }

    /**
     * 删除ASN信息
     *
     * @param asnId ASNID
     * @return 结果
     */
    @Override
    public int deleteTblAsnById(Long asnId)
    {
        tblAsnMapper.deleteTblAsnItemByAsnId(asnId);
        return tblAsnMapper.deleteTblAsnById(asnId);
    }

    /**
     * 查询ASN列表(接口专用)
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    @Override
    public List<TblAsn> selectTblAsnFullList(TblAsn tblAsn) {
        return tblAsnMapper.selectTblAsnFullList(tblAsn);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId() {
        return tblAsnMapper.selectLastId();
    }

    /**
     * 查询ASN(不包含行项目)
     *
     * @param asnId ASNID
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnOnlyById(Long asnId) {
        return tblAsnMapper.selectTblAsnOnlyById(asnId);
    }

    /**
     * 查询ASN行项目列表
     *
     * @param asnId ASNID
     * @return ASN行项目集合
     */
    @Override
    public List<TblAsnItem> selectTblAsnItemByAsnId(Long asnId) {
        return tblAsnMapper.selectTblAsnItemByAsnId(asnId);
    }

    /**
     * 查询ASN行项目
     *
     * @param itemId ItemID
     * @return ASN行项目
     */
    @Override
    public TblAsnItem selectTblAsnItemByItemId(Long itemId) {
        return tblAsnMapper.selectTblAsnItemByItemId(itemId);
    }

    /**
     * 查询ASN物料列表
     *
     * @param itemId ItemID
     * @return ASN物料列表
     */
    @Override
    public List<TblAsnArticle> selectTblAsnArticleByItemId(Long itemId) {
        return tblAsnMapper.selectTblAsnArticleByItemId(itemId);
    }

    /**
     * 查询ASN行项目列表
     *
     * @param tblAsnItem TblAsnItem
     * @return ASN行项目集合
     */
    @Override
    public List<TblAsnItem> selectTblAsnItemList(TblAsnItem tblAsnItem) {
        return tblAsnMapper.selectTblAsnItemList(tblAsnItem);
    }

    /**
     * 查询ASN物料列表
     *
     * @param tblAsnArticle TblAsnArticle
     * @return ASN物料列表
     */
    @Override
    public List<TblAsnArticle> selectTblAsnArticleList(TblAsnArticle tblAsnArticle) {
        return tblAsnMapper.selectTblAsnArticleList(tblAsnArticle);
    }

    /**
     * 查询订单物料剩余
     *
     * @param orderAsnQuantity TblOrderAsnQuantity
     * @return 订单物料剩余集合
     */
    @Override
    public List<TblOrderAsnQuantity> selectTblOrderAsnQuantityList(TblOrderAsnQuantity orderAsnQuantity) {
        return tblAsnMapper.selectTblOrderAsnQuantityList(orderAsnQuantity);
    }

    @Override
    public AjaxResult printNpsSls(Long asnId, String tz) {
        String jasperPath = templatePath + "npsjsls.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        Map<String, Object> objectMap = genPrintDataForNpsSls(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public AjaxResult printPickingList(long asnId, String tz) {
        String jasperPath = templatePath + "pick_list.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        Map<String, Object> objectMap = genPrintDataForPickingList(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public TblAsn selectTblAsnForPrint(Long asnId) {
        TblAsn asn = new TblAsn();
        asn.setAsnId(asnId);
        return tblAsnMapper.selectTblAsnForPrint(asn);
    }

    /**
     * 新增ASN行项目信息
     *
     * @param tblAsn ASN对象
     */
    public void insertTblAsnItem(TblAsn tblAsn)
    {
        List<TblAsnItem> tblAsnItemList = tblAsn.getDetail();
        Long asnId = tblAsn.getAsnId();
        if (StringUtils.isNotNull(tblAsnItemList))
        {
            List<TblAsnItem> list = new ArrayList<TblAsnItem>();
            for (TblAsnItem tblAsnItem : tblAsnItemList)
            {
                tblAsnItem.setAsnId(asnId);
                list.add(tblAsnItem);
            }
            if (!list.isEmpty())
            {
                tblAsnMapper.batchTblAsnItem(list);
                List<TblAsnArticle> articles = new ArrayList<>();
                for (TblAsnItem item : tblAsnItemList){
                    for (TblAsnArticle article: item.getArticles()){
                        article.setItemId(item.getItemId());
                        // 校验物料是否存在
                        TblMaterial tblMaterial = tblMaterialService.selectTblMaterialByCodeAndPlant(article.getArticleNo(), item.getPlantCode());
                        if(tblMaterial == null || tblMaterial.getPlants().isEmpty()){
                            throw new RuntimeException(StringUtils.format("工厂" + item.getPlantCode() + "物料" + article.getArticleNo() + "配置不存在，请联系管理员"));
                        }
                        articles.add(article);
                    }
                }
                if(!articles.isEmpty()){
                    tblAsnMapper.batchTblAsnArticle(articles);
                }
            }

            tblAsnMapper.addOrderArticleQuantityByAsnId(tblAsn.getAsnId());
        }
    }

    private void genAsnCode(TblAsn tblAsn){
//        String asnCode = "ASN"+tblAsn.getCompCode()+tblAsn.getSuppCode();
        String asnCode = "ASN"+tblAsn.getSuppCode();
        String configKey = DataConstants.ASN_CODE_PARAM+tblAsn.getSuppCode();
        String currentCode = configService.selectConfigByKeyWithoutRedis(configKey);
        boolean isExist = true;
        if (StringUtils.isEmpty(currentCode)){
            currentCode = "0";
            isExist = false;
        }
        while(currentCode.length() < 5){
            currentCode = "0"+currentCode;
        }
        asnCode = asnCode+currentCode;
        SysConfig update = new SysConfig();
        update.setConfigKey(configKey);
        update.setConfigValue(String.valueOf(Integer.parseInt(currentCode)+1));
        int result = 0;
        if(!isExist){
            result = configService.insertConfigWithoutRedis(update);
        }else{
            result = configService.updateConfigValueByKeyWithoutRedis(update);
        }
        if (result != 1){
            log.error("ASN编号生成失败，{}", tblAsn.getSuppCode());
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.code.gen.error")));
        }
        tblAsn.setAsnCode(asnCode);
        for (int i = 0; i < tblAsn.getDetail().size(); i++){
            tblAsn.getDetail().get(i).setDnNo(asnCode+"-"+(i+1));
        }
    }

    private String getBarcodeStart(TblAsn tblAsn){
        String start = String.valueOf(tblAsn.getSuppCode().charAt(0));
        switch(start){
            case "1":
                return "5";
            case "2":
                return "6";
            default:
                return start;
        }
    }

    private void genBarCode(TblAsn tblAsn){
        String prefix = getBarcodeStart(tblAsn)+tblAsn.getSuppCode().substring(tblAsn.getSuppCode().length()-3);
        String barcodeKey = DataConstants.BAR_CODE_PARAM+"."+tblAsn.getCompCode();
        int currentCodeSeq = Integer.parseInt(configService.selectConfigByKey(barcodeKey));
        for (TblAsnItem item: tblAsn.getDetail()){
            for (TblAsnArticle article: item.getArticles()){
                StringBuilder currentCode = new StringBuilder(String.valueOf(currentCodeSeq));
                while(currentCode.length() < 6){
                    currentCode.insert(0, "0");
                }
                String startWith = prefix+ currentCode;
                currentCodeSeq = article.getPackQty().intValue() + currentCodeSeq;
                StringBuilder endCode = new StringBuilder(String.valueOf(currentCodeSeq - 1));
                while(endCode.length() < 6){
                    endCode.insert(0, "0");
                }
                String endWith = prefix + endCode;
                article.setStartWith(startWith);
                article.setEndWith(endWith);
            }
        }
        SysConfig update = new SysConfig();
        update.setConfigKey(barcodeKey);
        update.setConfigValue(String.valueOf(currentCodeSeq));
        configService.updateConfigValueByKey(update);
    }

    private HashMap sendToSap(TblAsn tblAsn){
        List<TblOrder> orderList = new ArrayList<>();
        Set<String> orderCodeList = tblAsn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            orderList.addAll(orderService.selectTblOrderWithItemList(search));
        }
        
        try {
            // 直接获取SAP请求体
            Map<String, Object> requestBody = SapObjectConverter.convertToSapAsn(tblAsn, orderList);
            
            // 调用SAP接口
            String sapUrl = configService.selectConfigByKey(DataConstants.SAP_ASN_URL);
            if (StringUtils.isEmpty(sapUrl)) {
                throw new RuntimeException("SAP订单确认接口URL未配置");
            }
            HashMap<String, Object> result = sapApiClient.callSapApi(sapUrl, requestBody);
            
            log.info("Asn {} send to sap result:{}", tblAsn.getAsnCode(), result);
            
            // 检查返回结果
            if (result != null) {
                // 检查是否有错误信息
                if (result.containsKey("MSTYP") && "E".equals(result.get("MSTYP"))) {
                    String errorMsg = (String) result.get("MSG");
                    throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), errorMsg != null ? errorMsg : "SAP接口异常"));
                }
                if (result.containsKey("MSTYP") && "S".equals(result.get("MSTYP"))) {
                    String docNo = (String) result.get("VBELN");
                    if (StringUtils.isNotEmpty(docNo)) {
                        tblAsn.setDocNo(docNo); // SAP返回的交货单号
                        tblAsnMapper.updateTblAsn(tblAsn);
                    }
//                    else {
//                        throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), "SAP接口返回的单据号为空"));
//                    }
                }
            } else {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), "SAP接口返回为空"));
            }
            
            return result;
        } catch (RuntimeException e) {
            log.error("发送ASN到SAP时发生错误", e);
            throw e;
        }
    }

    private Map<String, Object> genPrintData(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, TblOrder> orderMap = new HashMap<>();
        Set<String> orderCodeList = asn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            orderMap.put(orderCode, orderService.selectTblOrderWithItemList(search).get(0));
        }
        Map<String, Object> result = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("compCode", asn.getCompCode());
        headerMap.put("plantCode", asn.getDetail().get(0).getPlantCode());
        headerMap.put("compName", orderMap.get(asn.getDetail().get(0).getOrderCode()).getCompName());
        headerMap.put("asnCode", asn.getAsnCode());
        headerMap.put("deliveryDate", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd", timeZone));
        headerMap.put("deliveryTime", DateFormatUtils.format(asn.getDeliveryDate(), "HH:mm", timeZone));
        headerMap.put("suppCode", asn.getSuppCode());
        headerMap.put("suppName", asn.getSuppName());
        headerMap.put("depot", orderMap.get(asn.getDetail().get(0).getOrderCode()).getDetail().get(0).getDepot());
        headerMap.put("deliveryDateTime", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd HH:mm", timeZone));
        headerMap.put("unloadingNo", asn.getDetail().get(0).getUnloadingNo());
        headerMap.put("createDate", DateFormatUtils.format(asn.getCreateTime(), "yyyy/MM/dd"));
        StringBuilder qrSb = new StringBuilder();
        List<Map<String, Object>> dataMapList = new ArrayList<>();
        int lineCode = 1;
        for (TblAsnItem item:asn.getDetail()){
            for (TblAsnArticle article: item.getArticles()){
                qrSb.append(Util.fillAfter(item.getOrderCode(),10, ' '));
                qrSb.append(Util.fillAfter(article.getArticleNo(), 25, ' '));
                qrSb.append(Util.fillBefore(article.getQuantity().toString(), 10, '0'));
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("orderCode", item.getOrderCode());
                String articleNo = article.getArticleNo();
                String productClass = SapObjectConverter.handleProductClass(dictDataService, articleNo);
                if (StringUtils.isNotEmpty(productClass)) {
                    articleNo = article.getArticleNo().substring(0, article.getArticleNo().indexOf("+"));
                }
                dataMap.put("productClass", productClass);
                dataMap.put("articleNo", articleNo);
                dataMap.put("quantity", article.getQuantity());
                dataMap.put("unit", article.getUnit());
                dataMapList.add(dataMap);
            }
        }

        headerMap.put("qrCode", Util.fillAfter(qrSb.toString(), 900, ' '));
        result.put("header", headerMap);
        result.put("data", dataMapList);
        return result;
    }

    private Map<String, Object> genPrintDataForPickingList(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, TblOrder> orderMap = new HashMap<>();
        Set<String> orderCodeList = asn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            List<TblOrder> orders = orderService.selectTblOrderWithItemList(search);
            if (!orders.isEmpty()) {
                orderMap.put(orderCode, orders.get(0));
            }
        }
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息 - 现在suppName已经从数据库中获取了正确的值
        headerMap.put("suppCode", asn.getSuppCode());
        headerMap.put("suppName", asn.getSuppName()); // 这里已经是从sys_dept表关联获取的dept_name
        headerMap.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd", timeZone));

        // 获取当前登录用户作为打印人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String printedBy = loginUser != null ? loginUser.getUser().getNickName() : "System";
        headerMap.put("printedBy", printedBy);



        List<Map<String, Object>> dataMapList = new ArrayList<>();
        int lineCode = 1;

        for (TblAsnItem item : asn.getDetail()){
            TblOrder order = orderMap.get(item.getOrderCode());
            for (TblAsnArticle article : item.getArticles()){
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("asnno", asn.getAsnCode());
                dataMap.put("orderNo", item.getOrderCode());
                dataMap.put("orderLineNo", article.getOrderLineNo());
                dataMap.put("releaseNo", article.getDeliveryScheduleNo());
                dataMap.put("partNo", article.getArticleNo());
                // 这里的articleName已经是从tbl_material表关联获取的Material_Name，如果没有则使用原来的Article_Name
                dataMap.put("partDescription", article.getArticleName());

                // 从订单中获取到期日期
                String dueDate = "";
                if (order != null && order.getDetail() != null) {
                    for (TblOrderItem orderItem : order.getDetail()) {
                        if (orderItem.getItemNo().equals(article.getOrderLineNo())) {
                            if (orderItem.getDeliveryDate() != null) {
                                dueDate = DateFormatUtils.format(orderItem.getDeliveryDate(), "yyyy-MM-dd", timeZone);
                            }
                            break;
                        }
                    }
                }
                dataMap.put("dueDate", dueDate);

                dataMap.put("shipQty", article.getQuantity());
                dataMap.put("unit", article.getUnit());
                dataMapList.add(dataMap);
            }
        }

        result.put("header", headerMap);
        result.put("data", dataMapList);
        return result;
    }

    private Map<String, Object> genPrintDataForNpsSls(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息
        headerMap.put("asnno", asn.getAsnCode());
        headerMap.put("sapasnNo", asn.getDocNo() != null ? asn.getDocNo() : "");
        headerMap.put("supplierName", asn.getSuppName() != null ? asn.getSuppName() : "");
        headerMap.put("deliveryDate", asn.getDeliveryDate() != null ?
            DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd", timeZone) : "");

        // 公司信息暂时留空
        headerMap.put("companyName", "");
        headerMap.put("companyAddress", "");
        headerMap.put("c_Phone", "");
        headerMap.put("c_FAX", "");
        headerMap.put("phone", "");
        headerMap.put("fax", "");

        // 工厂信息
        if (asn.getDetail() != null && !asn.getDetail().isEmpty()) {
            TblAsnItem firstItem = asn.getDetail().get(0);
            headerMap.put("factoryCode", firstItem.getPlantCode() != null ? firstItem.getPlantCode() : "");
            headerMap.put("deliveryAddress", firstItem.getUnloadingName() != null ? firstItem.getUnloadingName() : "");
        } else {
            headerMap.put("factoryCode", "");
            headerMap.put("deliveryAddress", "");
        }

        // 获取当前登录用户作为打印人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String printedBy = loginUser != null ? loginUser.getUser().getNickName() : "System";
        headerMap.put("userLoginName", printedBy);

        // 生成物料明细数据
        List<Map<String, Object>> allDataList = new ArrayList<>();
        int lineCode = 1;

        for (TblAsnItem item : asn.getDetail()){
            for (TblAsnArticle article : item.getArticles()){
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("partNo", article.getArticleNo());
                dataMap.put("partDescription", article.getArticleName() != null ? article.getArticleName() : "");
                dataMap.put("orderNo", item.getOrderCode());
                dataMap.put("orderLineNo", article.getOrderLineNo() != null ? article.getOrderLineNo() : "");
                dataMap.put("releaseNo", article.getDeliveryScheduleNo() != null ? article.getDeliveryScheduleNo() : "");
                dataMap.put("shipQty", article.getQuantity());
                dataMap.put("remark", ""); // 备注暂时留空
                allDataList.add(dataMap);
            }
        }

        // 按每页5行进行分页处理
        List<List<Map<String, Object>>> pagedData = new ArrayList<>();
        for (int i = 0; i < allDataList.size(); i += 5) {
            int endIndex = Math.min(i + 5, allDataList.size());
            List<Map<String, Object>> pageData = new ArrayList<>(allDataList.subList(i, endIndex));

            // 如果不足5行，补充空行
            while (pageData.size() < 5) {
                Map<String, Object> emptyRow = new HashMap<>();
                emptyRow.put("lineNo", "");
                emptyRow.put("partNo", "");
                emptyRow.put("partDescription", "");
                emptyRow.put("orderNo", "");
                emptyRow.put("orderLineNo", "");
                emptyRow.put("releaseNo", "");
                emptyRow.put("shipQty", "");
                emptyRow.put("remark", "");
                pageData.add(emptyRow);
            }
            pagedData.add(pageData);
        }

        headerMap.put("asnList", pagedData);
        result.put("header", headerMap);
        result.put("data", allDataList); // JasperReports需要的数据源
        return result;
    }
}
